/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tooltip_tooltip___5Rrn {
  --tooltip-bg: #1d1d1f;
  --tooltip-color: #fafafa;
  --tooltip-border-radius: 0.5rem;
  --tooltip-padding: 0.5rem 0.75rem;
  --tooltip-font-size: 0.875rem;
  --tooltip-line-height: 1.4;
  --tooltip-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  --arrow-size: 0.375rem;
  background-color: var(--tooltip-bg);
  color: var(--tooltip-color);
  border-radius: var(--tooltip-border-radius);
  box-shadow: var(--tooltip-shadow);
  font-size: var(--tooltip-font-size);
  line-height: var(--tooltip-line-height);
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.2s var(--ease-transition), transform 0.2s var(--ease-transition);
  pointer-events: none;
  word-wrap: break-word;
  hyphens: auto;
}
.tooltip_tooltip--visible__nvxl2 {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}
.tooltip_tooltip--top__imso3 {
  transform-origin: bottom center;
}
.tooltip_tooltip--top__imso3:not(.tooltip_tooltip--visible__nvxl2) {
  transform: scale(0.95) translateY(0.25rem);
}
.tooltip_tooltip--bottom__M66nw {
  transform-origin: top center;
}
.tooltip_tooltip--bottom__M66nw:not(.tooltip_tooltip--visible__nvxl2) {
  transform: scale(0.95) translateY(-0.25rem);
}
.tooltip_tooltip--left___fBEt {
  transform-origin: right center;
}
.tooltip_tooltip--left___fBEt:not(.tooltip_tooltip--visible__nvxl2) {
  transform: scale(0.95) translateX(0.25rem);
}
.tooltip_tooltip--right__6nOG_ {
  transform-origin: left center;
}
.tooltip_tooltip--right__6nOG_:not(.tooltip_tooltip--visible__nvxl2) {
  transform: scale(0.95) translateX(-0.25rem);
}

.tooltip_content__1MR4m {
  padding: var(--tooltip-padding);
  position: relative;
  z-index: 1;
}

.tooltip_arrow__Z_UcB {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}
.tooltip_arrow--top__LLrGn {
  bottom: -0.375rem;
  left: 50%;
  transform: translateX(-50%);
  border-width: var(--arrow-size) var(--arrow-size) 0 var(--arrow-size);
  border-color: var(--tooltip-bg) transparent transparent transparent;
}
.tooltip_arrow--bottom__vTN_F {
  top: -0.375rem;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 var(--arrow-size) var(--arrow-size) var(--arrow-size);
  border-color: transparent transparent var(--tooltip-bg) transparent;
}
.tooltip_arrow--left__kAp_O {
  right: -0.375rem;
  top: 50%;
  transform: translateY(-50%);
  border-width: var(--arrow-size) 0 var(--arrow-size) var(--arrow-size);
  border-color: transparent transparent transparent var(--tooltip-bg);
}
.tooltip_arrow--right__OQ_7R {
  left: -0.375rem;
  top: 50%;
  transform: translateY(-50%);
  border-width: var(--arrow-size) var(--arrow-size) var(--arrow-size) 0;
  border-color: transparent var(--tooltip-bg) transparent transparent;
}

.tooltip_tooltip--light__OJeDI {
  --tooltip-bg: #fafafa;
  --tooltip-color: #1d1d1f;
  border: 1px solid #dddde3;
}
.tooltip_tooltip--error__R7c1m {
  --tooltip-bg: #ee1d52;
  --tooltip-color: #fafafa;
}
.tooltip_tooltip--success__Sze0C {
  --tooltip-bg: #69c069;
  --tooltip-color: #fafafa;
}
.tooltip_tooltip--warning__SfQTX {
  --tooltip-bg: #c37f00;
  --tooltip-color: #1d1d1f;
}

.tooltip_tooltip--small__rD4_r {
  --tooltip-font-size: 0.75rem;
  --tooltip-padding: 0.25rem 0.5rem;
  --arrow-size: 0.25rem;
}
.tooltip_tooltip--large__uNDqi {
  --tooltip-font-size: 1rem;
  --tooltip-padding: 0.75rem 1rem;
  --arrow-size: 0.5rem;
}

@media not all and (min-width: 48rem) {
  .tooltip_tooltip___5Rrn {
    --tooltip-font-size: 0.8125rem;
    max-width: calc(100vw - 1rem) !important;
  }
}
@media (prefers-contrast: high) {
  .tooltip_tooltip___5Rrn {
    border: 2px solid currentColor;
    --tooltip-shadow: 0 0 0 1px var(--tooltip-bg);
  }
}
@media (prefers-reduced-motion: reduce) {
  .tooltip_tooltip___5Rrn {
    transition: opacity 0.1s ease;
  }
  .tooltip_tooltip___5Rrn:not(.tooltip_tooltip--visible__nvxl2) {
    transform: none;
  }
}
