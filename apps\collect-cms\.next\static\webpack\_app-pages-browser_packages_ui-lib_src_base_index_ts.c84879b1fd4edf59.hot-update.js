/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_packages_ui-lib_src_base_index_ts",{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss":
/*!*************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss ***!
  \*************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"tooltip\":\"tooltip_tooltip___5Rrn\",\"tooltip--visible\":\"tooltip_tooltip--visible__nvxl2\",\"tooltip--top\":\"tooltip_tooltip--top__imso3\",\"tooltip--bottom\":\"tooltip_tooltip--bottom__M66nw\",\"tooltip--left\":\"tooltip_tooltip--left___fBEt\",\"tooltip--right\":\"tooltip_tooltip--right__6nOG_\",\"content\":\"tooltip_content__1MR4m\",\"arrow\":\"tooltip_arrow__Z_UcB\",\"arrow--top\":\"tooltip_arrow--top__LLrGn\",\"arrow--bottom\":\"tooltip_arrow--bottom__vTN_F\",\"arrow--left\":\"tooltip_arrow--left__kAp_O\",\"arrow--right\":\"tooltip_arrow--right__OQ_7R\",\"tooltip--light\":\"tooltip_tooltip--light__OJeDI\",\"tooltip--error\":\"tooltip_tooltip--error__R7c1m\",\"tooltip--success\":\"tooltip_tooltip--success__Sze0C\",\"tooltip--warning\":\"tooltip_tooltip--warning__SfQTX\",\"tooltip--small\":\"tooltip_tooltip--small__rD4_r\",\"tooltip--large\":\"tooltip_tooltip--large__uNDqi\"};\n    if(true) {\n      // 1748687824713\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"8b49173bbf70\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _useTooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useTooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tooltip.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Tooltip auto */ \n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar Tooltip = function(param) {\n    var content = param.content, _param_placement = param.placement, placement = _param_placement === void 0 ? \"top\" : _param_placement, _param_trigger = param.trigger, trigger = _param_trigger === void 0 ? \"hover\" : _param_trigger, _param_delay = param.delay, delay = _param_delay === void 0 ? 200 : _param_delay, _param_disabled = param.disabled, disabled = _param_disabled === void 0 ? false : _param_disabled, className = param.className, contentClassName = param.contentClassName, children = param.children, _param_showArrow = param.showArrow, showArrow = _param_showArrow === void 0 ? true : _param_showArrow, _param_maxWidth = param.maxWidth, maxWidth = _param_maxWidth === void 0 ? 300 : _param_maxWidth;\n    _s();\n    var _useTooltip = (0,_useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip)({\n        placement: placement,\n        trigger: trigger,\n        delay: delay,\n        disabled: disabled\n    }), isVisible = _useTooltip.isVisible, position = _useTooltip.position, triggerRef = _useTooltip.triggerRef, tooltipRef = _useTooltip.tooltipRef, triggerProps = _useTooltip.triggerProps;\n    // Don't render if disabled or no content\n    if (disabled || !content) {\n        return children;\n    }\n    // Clone the trigger element and add event handlers\n    var triggerElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.isValidElement)(children) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.cloneElement)(children, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, triggerProps), {\n        ref: function(node) {\n            triggerRef.current = node;\n            // Preserve existing ref if any\n            if (typeof children.ref === \"function\") {\n                children.ref(node);\n            } else if (children.ref) {\n                children.ref.current = node;\n            }\n        }\n    })) : children;\n    var _obj;\n    var tooltipContent = isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: tooltipRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().tooltip), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"tooltip--\".concat(position.placement)], (_obj = {}, (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--visible\"]), isVisible), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--with-arrow\"]), showArrow), _obj), className),\n        style: {\n            position: \"fixed\",\n            top: position.top,\n            left: position.left,\n            maxWidth: \"\".concat(maxWidth, \"px\"),\n            zIndex: 1000\n        },\n        role: \"tooltip\",\n        \"aria-hidden\": !isVisible,\n        children: [\n            showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().arrow), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"arrow--\".concat(position.placement)])\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 94,\n                columnNumber: 18\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().content), contentClassName),\n                children: content\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 95,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, _this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            triggerElement,\n             true && tooltipContent && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(tooltipContent, document.body)\n        ]\n    }, void 0, true);\n};\n_s(Tooltip, \"mCjxUs5jRDdeKrBOZfcGYi7AnyE=\", false, function() {\n    return [\n        _useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip\n    ];\n});\n_c = Tooltip;\nTooltip.displayName = \"Tooltip\";\nvar _c;\n$RefreshReg$(_c, \"Tooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/index.ts":
/*!**************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/index.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* reexport safe */ _Tooltip__WEBPACK_IMPORTED_MODULE_0__.Tooltip; },\n/* harmony export */   useTooltip: function() { return /* reexport safe */ _useTooltip__WEBPACK_IMPORTED_MODULE_1__.useTooltip; }\n/* harmony export */ });\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\");\n/* harmony import */ var _useTooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useTooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\");\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1Rvb2x0aXAvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5QjtBQUNHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL1Rvb2x0aXAvaW5kZXgudHM/MDBlOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL1Rvb2x0aXAnXG5leHBvcnQgKiBmcm9tICcuL3VzZVRvb2x0aXAnXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTooltip: function() { return /* binding */ useTooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTooltip auto */ \n\nvar useTooltip = function() {\n    var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, _ref_placement = _ref.placement, placement = _ref_placement === void 0 ? \"top\" : _ref_placement, _ref_trigger = _ref.trigger, trigger = _ref_trigger === void 0 ? \"hover\" : _ref_trigger, _ref_delay = _ref.delay, delay = _ref_delay === void 0 ? 200 : _ref_delay, _ref_disabled = _ref.disabled, disabled = _ref_disabled === void 0 ? false : _ref_disabled;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__._)((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), 2), isVisible = _useState[0], setIsVisible = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__._)((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        top: 0,\n        left: 0,\n        placement: placement\n    }), 2), position = _useState1[0], setPosition = _useState1[1];\n    var triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var calculatePosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (!triggerRef.current || !tooltipRef.current) return;\n        var triggerRect = triggerRef.current.getBoundingClientRect();\n        var tooltipRect = tooltipRef.current.getBoundingClientRect();\n        var viewport = {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n        var finalPlacement = placement;\n        var top = 0;\n        var left = 0;\n        // Calculate initial position based on placement\n        switch(placement){\n            case \"top\":\n                top = triggerRect.top - tooltipRect.height - 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"bottom\":\n                top = triggerRect.bottom + 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"left\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.left - tooltipRect.width - 8;\n                break;\n            case \"right\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.right + 8;\n                break;\n        }\n        // Auto-adjust if tooltip goes outside viewport\n        if (top < 8) {\n            finalPlacement = \"bottom\";\n            top = triggerRect.bottom + 8;\n        } else if (top + tooltipRect.height > viewport.height - 8) {\n            finalPlacement = \"top\";\n            top = triggerRect.top - tooltipRect.height - 8;\n        }\n        if (left < 8) {\n            left = 8;\n        } else if (left + tooltipRect.width > viewport.width - 8) {\n            left = viewport.width - tooltipRect.width - 8;\n        }\n        setPosition({\n            top: top,\n            left: left,\n            placement: finalPlacement\n        });\n    }, [\n        placement\n    ]);\n    var showTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (disabled) return;\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(function() {\n            setIsVisible(true);\n        }, delay);\n    }, [\n        delay,\n        disabled\n    ]);\n    var hideTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(function() {\n            setIsVisible(false);\n        }, delay);\n    }, [\n        delay\n    ]);\n    var handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"hover\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    var handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"hover\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    var handleClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"click\") {\n            if (isVisible) {\n                hideTooltip();\n            } else {\n                showTooltip();\n            }\n        }\n    }, [\n        trigger,\n        isVisible,\n        showTooltip,\n        hideTooltip\n    ]);\n    var handleFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"focus\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    var handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"focus\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    // Update position when tooltip becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (isVisible) {\n            calculatePosition();\n        }\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        var handleResize = function() {\n            if (isVisible) {\n                calculatePosition();\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        return function() {\n            return window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle click outside for click trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (trigger !== \"click\" || !isVisible) return;\n        var handleClickOutside = function(event) {\n            if (triggerRef.current && tooltipRef.current && !triggerRef.current.contains(event.target) && !tooltipRef.current.contains(event.target)) {\n                hideTooltip();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            return document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        trigger,\n        isVisible,\n        hideTooltip\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        return function() {\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n        };\n    }, []);\n    return {\n        isVisible: isVisible,\n        position: position,\n        triggerRef: triggerRef,\n        tooltipRef: tooltipRef,\n        triggerProps: {\n            onMouseEnter: handleMouseEnter,\n            onMouseLeave: handleMouseLeave,\n            onClick: handleClick,\n            onFocus: handleFocus,\n            onBlur: handleBlur\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/index.ts":
/*!******************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/index.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: function() { return /* reexport safe */ _BlockContainer__WEBPACK_IMPORTED_MODULE_0__.BlockContainer; },\n/* harmony export */   BlockContent: function() { return /* reexport safe */ _BlockContent__WEBPACK_IMPORTED_MODULE_4__.BlockContent; },\n/* harmony export */   BlockHorizon: function() { return /* reexport safe */ _BlockHorizon__WEBPACK_IMPORTED_MODULE_3__.BlockHorizon; },\n/* harmony export */   Color: function() { return /* reexport safe */ _Color__WEBPACK_IMPORTED_MODULE_5__.Color; },\n/* harmony export */   Divider: function() { return /* reexport safe */ _Divider__WEBPACK_IMPORTED_MODULE_7__.Divider; },\n/* harmony export */   GuidelineLink: function() { return /* reexport safe */ _GuidelineLink__WEBPACK_IMPORTED_MODULE_2__.GuidelineLink; },\n/* harmony export */   Header: function() { return /* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_9__.Header; },\n/* harmony export */   Media: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_6__.Media; },\n/* harmony export */   NavigationWrap: function() { return /* reexport safe */ _NavigationWrap__WEBPACK_IMPORTED_MODULE_10__.NavigationWrap; },\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_1__.SearchBar; },\n/* harmony export */   TextHorizon: function() { return /* reexport safe */ _TextHorizon__WEBPACK_IMPORTED_MODULE_8__.TextHorizon; },\n/* harmony export */   Tooltip: function() { return /* reexport safe */ _Tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip; },\n/* harmony export */   useTooltip: function() { return /* reexport safe */ _Tooltip__WEBPACK_IMPORTED_MODULE_11__.useTooltip; }\n/* harmony export */ });\n/* harmony import */ var _BlockContainer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContainer */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts\");\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SearchBar */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/index.ts\");\n/* harmony import */ var _GuidelineLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GuidelineLink */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/index.ts\");\n/* harmony import */ var _BlockHorizon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BlockHorizon */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts\");\n/* harmony import */ var _BlockContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BlockContent */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/BlockContent/index.ts\");\n/* harmony import */ var _Color__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Color */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/index.ts\");\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Media */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/index.ts\");\n/* harmony import */ var _Divider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Divider */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Divider/index.ts\");\n/* harmony import */ var _TextHorizon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TextHorizon */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/index.ts\");\n/* harmony import */ var _NavigationWrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NavigationWrap */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/index.ts\");\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Tooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/index.ts\");\n// Export modules\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxpQkFBaUI7QUFDZTtBQUNMO0FBQ0k7QUFDRDtBQUNBO0FBQ1A7QUFDQTtBQUNFO0FBQ0k7QUFDTDtBQUNRO0FBQ1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vaW5kZXgudHM/ODAxYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnQgbW9kdWxlc1xuZXhwb3J0ICogZnJvbSAnLi9CbG9ja0NvbnRhaW5lcidcbmV4cG9ydCAqIGZyb20gJy4vU2VhcmNoQmFyJ1xuZXhwb3J0ICogZnJvbSAnLi9HdWlkZWxpbmVMaW5rJ1xuZXhwb3J0ICogZnJvbSAnLi9CbG9ja0hvcml6b24nXG5leHBvcnQgKiBmcm9tICcuL0Jsb2NrQ29udGVudCdcbmV4cG9ydCAqIGZyb20gJy4vQ29sb3InXG5leHBvcnQgKiBmcm9tICcuL01lZGlhJ1xuZXhwb3J0ICogZnJvbSAnLi9EaXZpZGVyJ1xuZXhwb3J0ICogZnJvbSAnLi9UZXh0SG9yaXpvbidcbmV4cG9ydCAqIGZyb20gJy4vSGVhZGVyJ1xuZXhwb3J0ICogZnJvbSAnLi9OYXZpZ2F0aW9uV3JhcCdcbmV4cG9ydCAqIGZyb20gJy4vVG9vbHRpcCdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/index.ts":
/*!***********************************************!*\
  !*** ../../packages/ui-lib/src/base/index.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockContainer; },\n/* harmony export */   BlockContent: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockContent; },\n/* harmony export */   BlockHorizon: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockHorizon; },\n/* harmony export */   Color: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Color; },\n/* harmony export */   Divider: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Divider; },\n/* harmony export */   GuidelineLink: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.GuidelineLink; },\n/* harmony export */   Header: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Header; },\n/* harmony export */   Media: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Media; },\n/* harmony export */   NavigationWrap: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.NavigationWrap; },\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.SearchBar; },\n/* harmony export */   TextHorizon: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.TextHorizon; },\n/* harmony export */   Tooltip: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Tooltip; },\n/* harmony export */   useTooltip: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.useTooltip; }\n/* harmony export */ });\n/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/index.ts\");\n // Wrapper should not be exported and needs to be import directly\n // for support dynamic imports with next/dynamic\n // export { Wrapper } from './Wrapper'\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0IsQ0FDeEIsaUVBQWlFO0NBQ2pFLGdEQUFnRDtDQUNoRCxzQ0FBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9pbmRleC50cz9jYzRmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vY29tbW9uJ1xuLy8gV3JhcHBlciBzaG91bGQgbm90IGJlIGV4cG9ydGVkIGFuZCBuZWVkcyB0byBiZSBpbXBvcnQgZGlyZWN0bHlcbi8vIGZvciBzdXBwb3J0IGR5bmFtaWMgaW1wb3J0cyB3aXRoIG5leHQvZHluYW1pY1xuLy8gZXhwb3J0IHsgV3JhcHBlciB9IGZyb20gJy4vV3JhcHBlcidcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/index.ts\n"));

/***/ })

});