"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_packages_ui-lib_src_base_common_Tooltip_index_ts",{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _useTooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useTooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tooltip.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Tooltip auto */ \n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar Tooltip = function(param) {\n    var content = param.content, _param_placement = param.placement, placement = _param_placement === void 0 ? \"top\" : _param_placement, _param_trigger = param.trigger, trigger = _param_trigger === void 0 ? \"hover\" : _param_trigger, _param_delay = param.delay, delay = _param_delay === void 0 ? 200 : _param_delay, _param_disabled = param.disabled, disabled = _param_disabled === void 0 ? false : _param_disabled, className = param.className, contentClassName = param.contentClassName, children = param.children, _param_showArrow = param.showArrow, showArrow = _param_showArrow === void 0 ? true : _param_showArrow, _param_maxWidth = param.maxWidth, maxWidth = _param_maxWidth === void 0 ? 300 : _param_maxWidth;\n    _s();\n    var _useTooltip = (0,_useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip)({\n        placement: placement,\n        trigger: trigger,\n        delay: delay,\n        disabled: disabled\n    }), isVisible = _useTooltip.isVisible, position = _useTooltip.position, triggerRef = _useTooltip.triggerRef, tooltipRef = _useTooltip.tooltipRef, triggerProps = _useTooltip.triggerProps;\n    // Don't render if disabled or no content\n    if (disabled || !content) {\n        return children;\n    }\n    // Clone the trigger element and add event handlers\n    var triggerElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.isValidElement)(children) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.cloneElement)(children, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, triggerProps), {\n        ref: function(node) {\n            triggerRef.current = node;\n            // Preserve existing ref if any\n            if (typeof children.ref === \"function\") {\n                children.ref(node);\n            } else if (children.ref) {\n                children.ref.current = node;\n            }\n        }\n    })) : children;\n    var _obj;\n    var tooltipContent = isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: tooltipRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().tooltip), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"tooltip--\".concat(position.placement)], (_obj = {}, (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--visible\"]), isVisible), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--with-arrow\"]), showArrow), _obj), className),\n        style: {\n            position: \"fixed\",\n            top: position.top,\n            left: position.left,\n            maxWidth: \"\".concat(maxWidth, \"px\"),\n            zIndex: 1000\n        },\n        role: \"tooltip\",\n        \"aria-hidden\": !isVisible,\n        children: [\n            showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().arrow), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"arrow--\".concat(position.placement)])\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 98,\n                columnNumber: 18\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().content), contentClassName),\n                children: content\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 99,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, _this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            triggerElement,\n             true && tooltipContent && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(tooltipContent, document.body)\n        ]\n    }, void 0, true);\n};\n_s(Tooltip, \"mCjxUs5jRDdeKrBOZfcGYi7AnyE=\", false, function() {\n    return [\n        _useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip\n    ];\n});\n_c = Tooltip;\nTooltip.displayName = \"Tooltip\";\nvar _c;\n$RefreshReg$(_c, \"Tooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\n"));

/***/ })

});