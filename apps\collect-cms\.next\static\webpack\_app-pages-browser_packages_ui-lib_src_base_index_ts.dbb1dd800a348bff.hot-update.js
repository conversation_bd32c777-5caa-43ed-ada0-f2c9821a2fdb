"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_packages_ui-lib_src_base_index_ts",{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _useTooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useTooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tooltip.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Tooltip auto */ \n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar Tooltip = function(param) {\n    var content = param.content, _param_placement = param.placement, placement = _param_placement === void 0 ? \"top\" : _param_placement, _param_trigger = param.trigger, trigger = _param_trigger === void 0 ? \"hover\" : _param_trigger, _param_delay = param.delay, delay = _param_delay === void 0 ? 200 : _param_delay, _param_disabled = param.disabled, disabled = _param_disabled === void 0 ? false : _param_disabled, className = param.className, contentClassName = param.contentClassName, children = param.children, _param_showArrow = param.showArrow, showArrow = _param_showArrow === void 0 ? true : _param_showArrow, _param_maxWidth = param.maxWidth, maxWidth = _param_maxWidth === void 0 ? 300 : _param_maxWidth, _param_variant = param.variant, variant = _param_variant === void 0 ? \"default\" : _param_variant, _param_size = param.size, size = _param_size === void 0 ? \"medium\" : _param_size;\n    _s();\n    var _useTooltip = (0,_useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip)({\n        placement: placement,\n        trigger: trigger,\n        delay: delay,\n        disabled: disabled\n    }), isVisible = _useTooltip.isVisible, position = _useTooltip.position, triggerRef = _useTooltip.triggerRef, tooltipRef = _useTooltip.tooltipRef, triggerProps = _useTooltip.triggerProps;\n    // Don't render if disabled or no content\n    if (disabled || !content) {\n        return children;\n    }\n    // Clone the trigger element and add event handlers\n    var triggerElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.isValidElement)(children) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.cloneElement)(children, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, triggerProps), {\n        ref: function(node) {\n            triggerRef.current = node;\n            // Preserve existing ref if any\n            if (typeof children.ref === \"function\") {\n                children.ref(node);\n            } else if (children.ref) {\n                children.ref.current = node;\n            }\n        }\n    })) : children;\n    var _obj;\n    var tooltipContent = isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: tooltipRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().tooltip), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"tooltip--\".concat(position.placement)], (_obj = {}, (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--visible\"]), isVisible), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--with-arrow\"]), showArrow), _obj), className),\n        style: {\n            position: \"fixed\",\n            top: position.top,\n            left: position.left,\n            maxWidth: \"\".concat(maxWidth, \"px\"),\n            zIndex: 1000\n        },\n        role: \"tooltip\",\n        \"aria-hidden\": !isVisible,\n        children: [\n            showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().arrow), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"arrow--\".concat(position.placement)])\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 100,\n                columnNumber: 18\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().content), contentClassName),\n                children: content\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 101,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, _this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            triggerElement,\n             true && tooltipContent && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(tooltipContent, document.body)\n        ]\n    }, void 0, true);\n};\n_s(Tooltip, \"mCjxUs5jRDdeKrBOZfcGYi7AnyE=\", false, function() {\n    return [\n        _useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip\n    ];\n});\n_c = Tooltip;\nTooltip.displayName = \"Tooltip\";\nvar _c;\n$RefreshReg$(_c, \"Tooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\n"));

/***/ })

});