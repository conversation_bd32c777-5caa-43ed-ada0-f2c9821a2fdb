.trigger {
	display: inline-block;
}

.tooltip {
	position: fixed;
	z-index: 9999;
	background-color: #1a1a1a;
	color: white;
	padding: 8px 12px;
	border-radius: 6px;
	font-size: 14px;
	max-width: 250px;
	word-wrap: break-word;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	opacity: 0;
	visibility: hidden;
	transition:
		opacity 0.2s ease,
		visibility 0.2s ease;
	pointer-events: none;

	&--visible {
		opacity: 1;
		visibility: visible;
		pointer-events: auto;
	}

	// Position variants with smooth animations
	&--top {
		transform: translateY(4px);

		&.tooltip--visible {
			transform: translateY(0);
		}
	}

	&--bottom {
		transform: translateY(-4px);

		&.tooltip--visible {
			transform: translateY(0);
		}
	}

	&--left {
		transform: translateX(4px);

		&.tooltip--visible {
			transform: translateX(0);
		}
	}

	&--right {
		transform: translateX(-4px);

		&.tooltip--visible {
			transform: translateX(0);
		}
	}
}

// Responsive design
@media (max-width: 768px) {
	.tooltip {
		max-width: calc(100vw - 16px);
		font-size: 13px;
		padding: 6px 10px;
	}
}

// High contrast mode
@media (prefers-contrast: high) {
	.tooltip {
		border: 2px solid white;
		background-color: black;
	}
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
	.tooltip {
		transition:
			opacity 0.1s ease,
			visibility 0.1s ease;

		&--top,
		&--bottom,
		&--left,
		&--right {
			transform: none;

			&.tooltip--visible {
				transform: none;
			}
		}
	}
}
