/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_Tooltip_TooltipExample_tsx";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_Tooltip_TooltipExample_tsx"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _useTooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useTooltip */ \"(ssr)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tooltip.module.scss */ \"(ssr)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Tooltip auto */ \n\n\n\n\n\nconst Tooltip = ({ content, placement = \"top\", trigger = \"hover\", delay = 200, disabled = false, className, contentClassName, children, showArrow = true, maxWidth = 300, variant = \"default\", size = \"medium\" })=>{\n    const { isVisible, position, triggerRef, tooltipRef, triggerProps } = (0,_useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip)({\n        placement,\n        trigger,\n        delay,\n        disabled\n    });\n    // Don't render if disabled or no content\n    if (disabled || !content) {\n        return children;\n    }\n    // Clone the trigger element and add event handlers\n    const triggerElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.isValidElement)(children) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.cloneElement)(children, {\n        ...triggerProps,\n        ref: (node)=>{\n            triggerRef.current = node;\n            // Preserve existing ref if any\n            if (typeof children.ref === \"function\") {\n                children.ref(node);\n            } else if (children.ref) {\n                children.ref.current = node;\n            }\n        }\n    }) : children;\n    const tooltipContent = isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: tooltipRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().tooltip), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[`tooltip--${position.placement}`], {\n            [(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--visible\"])]: isVisible,\n            [(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--with-arrow\"])]: showArrow,\n            [(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[`tooltip--${variant}`]]: variant !== \"default\",\n            [(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[`tooltip--${size}`]]: size !== \"medium\"\n        }, className),\n        style: {\n            position: \"fixed\",\n            top: position.top,\n            left: position.left,\n            maxWidth: `${maxWidth}px`,\n            zIndex: 1000\n        },\n        role: \"tooltip\",\n        \"aria-hidden\": !isVisible,\n        children: [\n            showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().arrow), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[`arrow--${position.placement}`])\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 102,\n                columnNumber: 18\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().content), contentClassName),\n                children: content\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 103,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            triggerElement,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true);\n};\nTooltip.displayName = \"Tooltip\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Tooltip/TooltipExample.tsx":
/*!************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/TooltipExample.tsx ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TooltipExample: () => (/* binding */ TooltipExample)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@collective/core */ \"(ssr)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Tooltip */ \"(ssr)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ TooltipExample auto */ \n\n\nconst TooltipExample = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"50px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: \"30px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Tooltip Examples\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 9,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Basic Usage\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"This is a basic tooltip\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Hover me\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"This tooltip appears on click\",\n                                trigger: \"click\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Click me\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Focus tooltip\",\n                                trigger: \"focus\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        padding: \"8px 16px\"\n                                    },\n                                    children: \"Focus me\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 12,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Placements\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(4, 1fr)\",\n                            gap: \"20px\",\n                            maxWidth: \"600px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Top tooltip\",\n                                placement: \"top\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Top\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Bottom tooltip\",\n                                placement: \"bottom\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Bottom\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Left tooltip\",\n                                placement: \"left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Right tooltip\",\n                                placement: \"right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 30,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Variants\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\",\n                            flexWrap: \"wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Default tooltip\",\n                                variant: \"default\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Default\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Light tooltip\",\n                                variant: \"light\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Light\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Error tooltip\",\n                                variant: \"error\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Success tooltip\",\n                                variant: \"success\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Warning tooltip\",\n                                variant: \"warning\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Warning\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 52,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Sizes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Small tooltip\",\n                                size: \"small\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Small\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Medium tooltip\",\n                                size: \"medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Large tooltip\",\n                                size: \"large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Large\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 78,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Complex Content\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Rich Content\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 9\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 9\n                                        }, void 0),\n                                        \"This tooltip contains \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                            children: \"formatted text\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 31\n                                        }, void 0),\n                                        \" and multiple lines.\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 9\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: \"It can include any React content!\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 9\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 8\n                                }, void 0),\n                                maxWidth: 250,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Rich Content\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"This is a very long tooltip content that will wrap to multiple lines to demonstrate how the tooltip handles longer text content gracefully.\",\n                                maxWidth: 200,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Long Text\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 96,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Without Arrow\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            content: \"Tooltip without arrow\",\n                            showArrow: false,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                children: \"No Arrow\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 6\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 124,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Custom Delay\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Fast tooltip (100ms)\",\n                                delay: 100,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Fast\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Slow tooltip (1000ms)\",\n                                delay: 1000,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Slow\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 134,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Disabled\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            content: \"This tooltip is disabled\",\n                            disabled: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                children: \"Disabled Tooltip\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 6\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 148,\n                columnNumber: 4\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Different Trigger Elements\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 5\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Tooltip on span\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        padding: \"8px\",\n                                        background: \"#f0f0f0\",\n                                        borderRadius: \"4px\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Hover this span\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Tooltip on image\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Crect width='40' height='40' fill='%23ddd'/%3E%3Ctext x='20' y='25' text-anchor='middle' fill='%23999'%3E\\uD83D\\uDCF7%3C/text%3E%3C/svg%3E\",\n                                    alt: \"Example\",\n                                    style: {\n                                        cursor: \"pointer\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 7\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 5\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 158,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Tooltip/TooltipExample.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTooltip: () => (/* binding */ useTooltip)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTooltip auto */ \nconst useTooltip = ({ placement = \"top\", trigger = \"hover\", delay = 200, disabled = false } = {})=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        top: 0,\n        left: 0,\n        placement\n    });\n    const triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const calculatePosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!triggerRef.current || !tooltipRef.current) return;\n        const triggerRect = triggerRef.current.getBoundingClientRect();\n        const tooltipRect = tooltipRef.current.getBoundingClientRect();\n        const viewport = {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n        let finalPlacement = placement;\n        let top = 0;\n        let left = 0;\n        // Calculate initial position based on placement\n        switch(placement){\n            case \"top\":\n                top = triggerRect.top - tooltipRect.height - 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"bottom\":\n                top = triggerRect.bottom + 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"left\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.left - tooltipRect.width - 8;\n                break;\n            case \"right\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.right + 8;\n                break;\n        }\n        // Auto-adjust if tooltip goes outside viewport\n        if (top < 8) {\n            finalPlacement = \"bottom\";\n            top = triggerRect.bottom + 8;\n        } else if (top + tooltipRect.height > viewport.height - 8) {\n            finalPlacement = \"top\";\n            top = triggerRect.top - tooltipRect.height - 8;\n        }\n        if (left < 8) {\n            left = 8;\n        } else if (left + tooltipRect.width > viewport.width - 8) {\n            left = viewport.width - tooltipRect.width - 8;\n        }\n        setPosition({\n            top,\n            left,\n            placement: finalPlacement\n        });\n    }, [\n        placement\n    ]);\n    const showTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (disabled) return;\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(()=>{\n            setIsVisible(true);\n        }, delay);\n    }, [\n        delay,\n        disabled\n    ]);\n    const hideTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(()=>{\n            setIsVisible(false);\n        }, delay);\n    }, [\n        delay\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"hover\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"hover\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"click\") {\n            if (isVisible) {\n                hideTooltip();\n            } else {\n                showTooltip();\n            }\n        }\n    }, [\n        trigger,\n        isVisible,\n        showTooltip,\n        hideTooltip\n    ]);\n    const handleFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"focus\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    const handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"focus\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    // Update position when tooltip becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (isVisible) {\n            calculatePosition();\n        }\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (isVisible) {\n                calculatePosition();\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle click outside for click trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (trigger !== \"click\" || !isVisible) return;\n        const handleClickOutside = (event)=>{\n            if (triggerRef.current && tooltipRef.current && !triggerRef.current.contains(event.target) && !tooltipRef.current.contains(event.target)) {\n                hideTooltip();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, [\n        trigger,\n        isVisible,\n        hideTooltip\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n        };\n    }, []);\n    return {\n        isVisible,\n        position,\n        triggerRef,\n        tooltipRef,\n        triggerProps: {\n            onMouseEnter: handleMouseEnter,\n            onMouseLeave: handleMouseLeave,\n            onClick: handleClick,\n            onFocus: handleFocus,\n            onBlur: handleBlur\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss":
/*!*************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss ***!
  \*************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"tooltip\": \"tooltip_tooltip___5Rrn\",\n\t\"tooltip--visible\": \"tooltip_tooltip--visible__nvxl2\",\n\t\"tooltip--top\": \"tooltip_tooltip--top__imso3\",\n\t\"tooltip--bottom\": \"tooltip_tooltip--bottom__M66nw\",\n\t\"tooltip--left\": \"tooltip_tooltip--left___fBEt\",\n\t\"tooltip--right\": \"tooltip_tooltip--right__6nOG_\",\n\t\"content\": \"tooltip_content__1MR4m\",\n\t\"arrow\": \"tooltip_arrow__Z_UcB\",\n\t\"arrow--top\": \"tooltip_arrow--top__LLrGn\",\n\t\"arrow--bottom\": \"tooltip_arrow--bottom__vTN_F\",\n\t\"arrow--left\": \"tooltip_arrow--left__kAp_O\",\n\t\"arrow--right\": \"tooltip_arrow--right__OQ_7R\",\n\t\"tooltip--light\": \"tooltip_tooltip--light__OJeDI\",\n\t\"tooltip--error\": \"tooltip_tooltip--error__R7c1m\",\n\t\"tooltip--success\": \"tooltip_tooltip--success__Sze0C\",\n\t\"tooltip--warning\": \"tooltip_tooltip--warning__SfQTX\",\n\t\"tooltip--small\": \"tooltip_tooltip--small__rD4_r\",\n\t\"tooltip--large\": \"tooltip_tooltip--large__uNDqi\"\n};\n\nmodule.exports.__checksum = \"228a511e5ca4\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9Ub29sdGlwL3Rvb2x0aXAubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVG9vbHRpcC90b29sdGlwLm1vZHVsZS5zY3NzP2M0MTQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidG9vbHRpcFwiOiBcInRvb2x0aXBfdG9vbHRpcF9fXzVScm5cIixcblx0XCJ0b29sdGlwLS12aXNpYmxlXCI6IFwidG9vbHRpcF90b29sdGlwLS12aXNpYmxlX19udnhsMlwiLFxuXHRcInRvb2x0aXAtLXRvcFwiOiBcInRvb2x0aXBfdG9vbHRpcC0tdG9wX19pbXNvM1wiLFxuXHRcInRvb2x0aXAtLWJvdHRvbVwiOiBcInRvb2x0aXBfdG9vbHRpcC0tYm90dG9tX19NNjZud1wiLFxuXHRcInRvb2x0aXAtLWxlZnRcIjogXCJ0b29sdGlwX3Rvb2x0aXAtLWxlZnRfX19mQkV0XCIsXG5cdFwidG9vbHRpcC0tcmlnaHRcIjogXCJ0b29sdGlwX3Rvb2x0aXAtLXJpZ2h0X182bk9HX1wiLFxuXHRcImNvbnRlbnRcIjogXCJ0b29sdGlwX2NvbnRlbnRfXzFNUjRtXCIsXG5cdFwiYXJyb3dcIjogXCJ0b29sdGlwX2Fycm93X19aX1VjQlwiLFxuXHRcImFycm93LS10b3BcIjogXCJ0b29sdGlwX2Fycm93LS10b3BfX0xMckduXCIsXG5cdFwiYXJyb3ctLWJvdHRvbVwiOiBcInRvb2x0aXBfYXJyb3ctLWJvdHRvbV9fdlROX0ZcIixcblx0XCJhcnJvdy0tbGVmdFwiOiBcInRvb2x0aXBfYXJyb3ctLWxlZnRfX2tBcF9PXCIsXG5cdFwiYXJyb3ctLXJpZ2h0XCI6IFwidG9vbHRpcF9hcnJvdy0tcmlnaHRfX09RXzdSXCIsXG5cdFwidG9vbHRpcC0tbGlnaHRcIjogXCJ0b29sdGlwX3Rvb2x0aXAtLWxpZ2h0X19PSmVESVwiLFxuXHRcInRvb2x0aXAtLWVycm9yXCI6IFwidG9vbHRpcF90b29sdGlwLS1lcnJvcl9fUjdjMW1cIixcblx0XCJ0b29sdGlwLS1zdWNjZXNzXCI6IFwidG9vbHRpcF90b29sdGlwLS1zdWNjZXNzX19TemUwQ1wiLFxuXHRcInRvb2x0aXAtLXdhcm5pbmdcIjogXCJ0b29sdGlwX3Rvb2x0aXAtLXdhcm5pbmdfX1NmUVRYXCIsXG5cdFwidG9vbHRpcC0tc21hbGxcIjogXCJ0b29sdGlwX3Rvb2x0aXAtLXNtYWxsX19yRDRfclwiLFxuXHRcInRvb2x0aXAtLWxhcmdlXCI6IFwidG9vbHRpcF90b29sdGlwLS1sYXJnZV9fdU5EcWlcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMjI4YTUxMWU1Y2E0XCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\n");

/***/ })

};
;