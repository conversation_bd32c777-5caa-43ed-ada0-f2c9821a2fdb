"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_Tooltip_useTooltip_ts"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTooltip: function() { return /* binding */ useTooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTooltip auto */ \n\nvar useTooltip = function() {\n    var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, _ref_placement = _ref.placement, placement = _ref_placement === void 0 ? \"top\" : _ref_placement, _ref_trigger = _ref.trigger, trigger = _ref_trigger === void 0 ? \"hover\" : _ref_trigger, _ref_delay = _ref.delay, delay = _ref_delay === void 0 ? 200 : _ref_delay, _ref_disabled = _ref.disabled, disabled = _ref_disabled === void 0 ? false : _ref_disabled;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__._)((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), 2), isVisible = _useState[0], setIsVisible = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__._)((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        top: 0,\n        left: 0,\n        placement: placement\n    }), 2), position = _useState1[0], setPosition = _useState1[1];\n    var triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var calculatePosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (!triggerRef.current || !tooltipRef.current) return;\n        var triggerRect = triggerRef.current.getBoundingClientRect();\n        var tooltipRect = tooltipRef.current.getBoundingClientRect();\n        var viewport = {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n        var finalPlacement = placement;\n        var top = 0;\n        var left = 0;\n        // Calculate initial position based on placement\n        switch(placement){\n            case \"top\":\n                top = triggerRect.top - tooltipRect.height - 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"bottom\":\n                top = triggerRect.bottom + 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"left\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.left - tooltipRect.width - 8;\n                break;\n            case \"right\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.right + 8;\n                break;\n        }\n        // Auto-adjust if tooltip goes outside viewport\n        if (top < 8) {\n            finalPlacement = \"bottom\";\n            top = triggerRect.bottom + 8;\n        } else if (top + tooltipRect.height > viewport.height - 8) {\n            finalPlacement = \"top\";\n            top = triggerRect.top - tooltipRect.height - 8;\n        }\n        if (left < 8) {\n            left = 8;\n        } else if (left + tooltipRect.width > viewport.width - 8) {\n            left = viewport.width - tooltipRect.width - 8;\n        }\n        setPosition({\n            top: top,\n            left: left,\n            placement: finalPlacement\n        });\n    }, [\n        placement\n    ]);\n    var showTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (disabled) return;\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(function() {\n            setIsVisible(true);\n        }, delay);\n    }, [\n        delay,\n        disabled\n    ]);\n    var hideTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(function() {\n            setIsVisible(false);\n        }, delay);\n    }, [\n        delay\n    ]);\n    var handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"hover\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    var handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"hover\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    var handleClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"click\") {\n            if (isVisible) {\n                hideTooltip();\n            } else {\n                showTooltip();\n            }\n        }\n    }, [\n        trigger,\n        isVisible,\n        showTooltip,\n        hideTooltip\n    ]);\n    var handleFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"focus\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    var handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"focus\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    // Update position when tooltip becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (isVisible) {\n            calculatePosition();\n        }\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        var handleResize = function() {\n            if (isVisible) {\n                calculatePosition();\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        return function() {\n            return window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle click outside for click trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (trigger !== \"click\" || !isVisible) return;\n        var handleClickOutside = function(event) {\n            if (triggerRef.current && tooltipRef.current && !triggerRef.current.contains(event.target) && !tooltipRef.current.contains(event.target)) {\n                hideTooltip();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            return document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        trigger,\n        isVisible,\n        hideTooltip\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        return function() {\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n        };\n    }, []);\n    return {\n        isVisible: isVisible,\n        position: position,\n        triggerRef: triggerRef,\n        tooltipRef: tooltipRef,\n        triggerProps: {\n            onMouseEnter: handleMouseEnter,\n            onMouseLeave: handleMouseLeave,\n            onClick: handleClick,\n            onFocus: handleFocus,\n            onBlur: handleBlur\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\n"));

/***/ })

}]);