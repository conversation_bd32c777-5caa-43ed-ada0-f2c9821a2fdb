"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_packages_ui-lib_src_base_index_ts",{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/index.ts":
/*!******************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/index.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: function() { return /* reexport safe */ _BlockContainer__WEBPACK_IMPORTED_MODULE_0__.BlockContainer; },\n/* harmony export */   BlockContent: function() { return /* reexport safe */ _BlockContent__WEBPACK_IMPORTED_MODULE_4__.BlockContent; },\n/* harmony export */   BlockHorizon: function() { return /* reexport safe */ _BlockHorizon__WEBPACK_IMPORTED_MODULE_3__.BlockHorizon; },\n/* harmony export */   Color: function() { return /* reexport safe */ _Color__WEBPACK_IMPORTED_MODULE_5__.Color; },\n/* harmony export */   Divider: function() { return /* reexport safe */ _Divider__WEBPACK_IMPORTED_MODULE_7__.Divider; },\n/* harmony export */   GuidelineLink: function() { return /* reexport safe */ _GuidelineLink__WEBPACK_IMPORTED_MODULE_2__.GuidelineLink; },\n/* harmony export */   Header: function() { return /* reexport safe */ _Header__WEBPACK_IMPORTED_MODULE_9__.Header; },\n/* harmony export */   Media: function() { return /* reexport safe */ _Media__WEBPACK_IMPORTED_MODULE_6__.Media; },\n/* harmony export */   NavigationWrap: function() { return /* reexport safe */ _NavigationWrap__WEBPACK_IMPORTED_MODULE_10__.NavigationWrap; },\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _SearchBar__WEBPACK_IMPORTED_MODULE_1__.SearchBar; },\n/* harmony export */   TextHorizon: function() { return /* reexport safe */ _TextHorizon__WEBPACK_IMPORTED_MODULE_8__.TextHorizon; }\n/* harmony export */ });\n/* harmony import */ var _BlockContainer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BlockContainer */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/BlockContainer/index.ts\");\n/* harmony import */ var _SearchBar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SearchBar */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/SearchBar/index.ts\");\n/* harmony import */ var _GuidelineLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GuidelineLink */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/GuidelineLink/index.ts\");\n/* harmony import */ var _BlockHorizon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BlockHorizon */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/BlockHorizon/index.ts\");\n/* harmony import */ var _BlockContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BlockContent */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/BlockContent/index.ts\");\n/* harmony import */ var _Color__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Color */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Color/index.ts\");\n/* harmony import */ var _Media__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Media */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Media/index.ts\");\n/* harmony import */ var _Divider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Divider */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Divider/index.ts\");\n/* harmony import */ var _TextHorizon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TextHorizon */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/TextHorizon/index.ts\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Header/index.ts\");\n/* harmony import */ var _NavigationWrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NavigationWrap */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/NavigationWrap/index.ts\");\n// Export modules\n\n\n\n\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxpQkFBaUI7QUFDZTtBQUNMO0FBQ0k7QUFDRDtBQUNBO0FBQ1A7QUFDQTtBQUNFO0FBQ0k7QUFDTDtBQUNRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvY29tbW9uL2luZGV4LnRzPzgwMWMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0IG1vZHVsZXNcbmV4cG9ydCAqIGZyb20gJy4vQmxvY2tDb250YWluZXInXG5leHBvcnQgKiBmcm9tICcuL1NlYXJjaEJhcidcbmV4cG9ydCAqIGZyb20gJy4vR3VpZGVsaW5lTGluaydcbmV4cG9ydCAqIGZyb20gJy4vQmxvY2tIb3Jpem9uJ1xuZXhwb3J0ICogZnJvbSAnLi9CbG9ja0NvbnRlbnQnXG5leHBvcnQgKiBmcm9tICcuL0NvbG9yJ1xuZXhwb3J0ICogZnJvbSAnLi9NZWRpYSdcbmV4cG9ydCAqIGZyb20gJy4vRGl2aWRlcidcbmV4cG9ydCAqIGZyb20gJy4vVGV4dEhvcml6b24nXG5leHBvcnQgKiBmcm9tICcuL0hlYWRlcidcbmV4cG9ydCAqIGZyb20gJy4vTmF2aWdhdGlvbldyYXAnXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/index.ts":
/*!***********************************************!*\
  !*** ../../packages/ui-lib/src/base/index.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlockContainer: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockContainer; },\n/* harmony export */   BlockContent: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockContent; },\n/* harmony export */   BlockHorizon: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.BlockHorizon; },\n/* harmony export */   Color: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Color; },\n/* harmony export */   Divider: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Divider; },\n/* harmony export */   GuidelineLink: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.GuidelineLink; },\n/* harmony export */   Header: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Header; },\n/* harmony export */   Media: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.Media; },\n/* harmony export */   NavigationWrap: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.NavigationWrap; },\n/* harmony export */   SearchBar: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.SearchBar; },\n/* harmony export */   TextHorizon: function() { return /* reexport safe */ _common__WEBPACK_IMPORTED_MODULE_0__.TextHorizon; }\n/* harmony export */ });\n/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/index.ts\");\n // Wrapper should not be exported and needs to be import directly\n // for support dynamic imports with next/dynamic\n // export { Wrapper } from './Wrapper'\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXdCLENBQ3hCLGlFQUFpRTtDQUNqRSxnREFBZ0Q7Q0FDaEQsc0NBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2Jhc2UvaW5kZXgudHM/Y2M0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2NvbW1vbidcbi8vIFdyYXBwZXIgc2hvdWxkIG5vdCBiZSBleHBvcnRlZCBhbmQgbmVlZHMgdG8gYmUgaW1wb3J0IGRpcmVjdGx5XG4vLyBmb3Igc3VwcG9ydCBkeW5hbWljIGltcG9ydHMgd2l0aCBuZXh0L2R5bmFtaWNcbi8vIGV4cG9ydCB7IFdyYXBwZXIgfSBmcm9tICcuL1dyYXBwZXInXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/index.ts\n"));

/***/ })

});