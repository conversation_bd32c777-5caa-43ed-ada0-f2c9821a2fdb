'use client'

import React from 'react'
import { Tooltip } from './Tooltip'

export const TooltipDemo: React.FC = () => {
	return (
		<div style={{ padding: '2rem' }}>
			<h2>Tooltip Component Demo</h2>
			<p>Đ<PERSON><PERSON> là demo component Tooltip dễ sử dụng, maintain và nâng cấp.</p>

			<div style={{ display: 'flex', gap: '1rem', marginTop: '2rem', flexWrap: 'wrap' }}>
				<Tooltip content="Tooltip đơn giản">
					<button>Hover để xem tooltip</button>
				</Tooltip>

				<Tooltip content="Tooltip với delay ngắn" delay={100}>
					<button>Fast tooltip</button>
				</Tooltip>

				<Tooltip content="Tooltip ở dưới" position="bottom">
					<button>Bottom tooltip</button>
				</Tooltip>

				<Tooltip content="Tooltip bên trái" position="left">
					<button>Left tooltip</button>
				</Tooltip>

				<Tooltip content="Tooltip bên phải" position="right">
					<button>Right tooltip</button>
				</Tooltip>

				<Tooltip content="Tooltip với nội dung dài hơn để test việc wrap text và hiển thị">
					<button>Long content</button>
				</Tooltip>

				<Tooltip
					content={
						<div>
							<strong>Rich content</strong>
							<br />
							Có thể chứa <em>HTML</em>
						</div>
					}
				>
					<button>Rich content</button>
				</Tooltip>
			</div>

			<div style={{ marginTop: '2rem' }}>
				<h3>Với các element khác nhau:</h3>
				<div style={{ display: 'flex', gap: '1rem', alignItems: 'center', marginTop: '1rem' }}>
					<Tooltip content="Tooltip trên span">
						<span style={{ padding: '8px', background: '#f0f0f0', borderRadius: '4px' }}>
							Hover span này
						</span>
					</Tooltip>

					<Tooltip content="Tooltip trên link">
						<a href="#" style={{ color: 'blue', textDecoration: 'underline' }}>
							Hover link này
						</a>
					</Tooltip>

					<Tooltip content="Tooltip trên div">
						<div
							style={{
								width: '40px',
								height: '40px',
								background: 'lightblue',
								borderRadius: '50%',
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
								cursor: 'pointer',
							}}
						>
							?
						</div>
					</Tooltip>
				</div>
			</div>
		</div>
	)
}
