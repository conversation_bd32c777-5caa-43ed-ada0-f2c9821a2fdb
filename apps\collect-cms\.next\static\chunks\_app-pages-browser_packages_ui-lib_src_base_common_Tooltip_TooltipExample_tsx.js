/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_packages_ui-lib_src_base_common_Tooltip_TooltipExample_tsx"],{

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss":
/*!*************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss ***!
  \*************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"tooltip\":\"tooltip_tooltip___5Rrn\",\"tooltip--visible\":\"tooltip_tooltip--visible__nvxl2\",\"tooltip--top\":\"tooltip_tooltip--top__imso3\",\"tooltip--bottom\":\"tooltip_tooltip--bottom__M66nw\",\"tooltip--left\":\"tooltip_tooltip--left___fBEt\",\"tooltip--right\":\"tooltip_tooltip--right__6nOG_\",\"content\":\"tooltip_content__1MR4m\",\"arrow\":\"tooltip_arrow__Z_UcB\",\"arrow--top\":\"tooltip_arrow--top__LLrGn\",\"arrow--bottom\":\"tooltip_arrow--bottom__vTN_F\",\"arrow--left\":\"tooltip_arrow--left__kAp_O\",\"arrow--right\":\"tooltip_arrow--right__OQ_7R\",\"tooltip--light\":\"tooltip_tooltip--light__OJeDI\",\"tooltip--error\":\"tooltip_tooltip--error__R7c1m\",\"tooltip--success\":\"tooltip_tooltip--success__Sze0C\",\"tooltip--warning\":\"tooltip_tooltip--warning__SfQTX\",\"tooltip--small\":\"tooltip_tooltip--small__rD4_r\",\"tooltip--large\":\"tooltip_tooltip--large__uNDqi\"};\n    if(true) {\n      // 1748687824713\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"8b49173bbf70\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx":
/*!*****************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _useTooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useTooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tooltip.module.scss */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/tooltip.module.scss\");\n/* harmony import */ var _tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Tooltip auto */ \n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar Tooltip = function(param) {\n    var content = param.content, _param_placement = param.placement, placement = _param_placement === void 0 ? \"top\" : _param_placement, _param_trigger = param.trigger, trigger = _param_trigger === void 0 ? \"hover\" : _param_trigger, _param_delay = param.delay, delay = _param_delay === void 0 ? 200 : _param_delay, _param_disabled = param.disabled, disabled = _param_disabled === void 0 ? false : _param_disabled, className = param.className, contentClassName = param.contentClassName, children = param.children, _param_showArrow = param.showArrow, showArrow = _param_showArrow === void 0 ? true : _param_showArrow, _param_maxWidth = param.maxWidth, maxWidth = _param_maxWidth === void 0 ? 300 : _param_maxWidth, _param_variant = param.variant, variant = _param_variant === void 0 ? \"default\" : _param_variant, _param_size = param.size, size = _param_size === void 0 ? \"medium\" : _param_size;\n    _s();\n    var _useTooltip = (0,_useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip)({\n        placement: placement,\n        trigger: trigger,\n        delay: delay,\n        disabled: disabled\n    }), isVisible = _useTooltip.isVisible, position = _useTooltip.position, triggerRef = _useTooltip.triggerRef, tooltipRef = _useTooltip.tooltipRef, triggerProps = _useTooltip.triggerProps;\n    // Don't render if disabled or no content\n    if (disabled || !content) {\n        return children;\n    }\n    // Clone the trigger element and add event handlers\n    var triggerElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.isValidElement)(children) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.cloneElement)(children, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, triggerProps), {\n        ref: function(node) {\n            triggerRef.current = node;\n            // Preserve existing ref if any\n            if (typeof children.ref === \"function\") {\n                children.ref(node);\n            } else if (children.ref) {\n                children.ref.current = node;\n            }\n        }\n    })) : children;\n    var _obj;\n    var tooltipContent = isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: tooltipRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().tooltip), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"tooltip--\".concat(position.placement)], (_obj = {}, (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--visible\"]), isVisible), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"tooltip--with-arrow\"]), showArrow), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"tooltip--\".concat(variant)], variant !== \"default\"), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_8__._)(_obj, (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"tooltip--\".concat(size)], size !== \"medium\"), _obj), className),\n        style: {\n            position: \"fixed\",\n            top: position.top,\n            left: position.left,\n            maxWidth: \"\".concat(maxWidth, \"px\"),\n            zIndex: 1000\n        },\n        role: \"tooltip\",\n        \"aria-hidden\": !isVisible,\n        children: [\n            showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().arrow), (_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"arrow--\".concat(position.placement)])\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 102,\n                columnNumber: 18\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_tooltip_module_scss__WEBPACK_IMPORTED_MODULE_4___default().content), contentClassName),\n                children: content\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n                lineNumber: 103,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\Tooltip.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, _this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            triggerElement,\n             true && tooltipContent && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(tooltipContent, document.body)\n        ]\n    }, void 0, true);\n};\n_s(Tooltip, \"mCjxUs5jRDdeKrBOZfcGYi7AnyE=\", false, function() {\n    return [\n        _useTooltip__WEBPACK_IMPORTED_MODULE_5__.useTooltip\n    ];\n});\n_c = Tooltip;\nTooltip.displayName = \"Tooltip\";\nvar _c;\n$RefreshReg$(_c, \"Tooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/TooltipExample.tsx":
/*!************************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/TooltipExample.tsx ***!
  \************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TooltipExample: function() { return /* binding */ TooltipExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _Tooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Tooltip */ \"(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/Tooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ TooltipExample auto */ var _this = undefined;\n\n\n\nvar TooltipExample = function() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"50px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: \"30px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: \"Tooltip Examples\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 9,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Basic Usage\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"This is a basic tooltip\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Hover me\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"This tooltip appears on click\",\n                                trigger: \"click\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Click me\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Focus tooltip\",\n                                trigger: \"focus\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        padding: \"8px 16px\"\n                                    },\n                                    children: \"Focus me\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 12,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Placements\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(4, 1fr)\",\n                            gap: \"20px\",\n                            maxWidth: \"600px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Top tooltip\",\n                                placement: \"top\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Top\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Bottom tooltip\",\n                                placement: \"bottom\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Bottom\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Left tooltip\",\n                                placement: \"left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Right tooltip\",\n                                placement: \"right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 30,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Variants\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\",\n                            flexWrap: \"wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Default tooltip\",\n                                variant: \"default\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Default\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Light tooltip\",\n                                variant: \"light\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Light\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Error tooltip\",\n                                variant: \"error\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Success tooltip\",\n                                variant: \"success\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Warning tooltip\",\n                                variant: \"warning\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Warning\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 52,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Sizes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Small tooltip\",\n                                size: \"small\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Small\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Medium tooltip\",\n                                size: \"medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Large tooltip\",\n                                size: \"large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Large\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 78,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Complex Content\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Rich Content\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 9\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 9\n                                        }, void 0),\n                                        \"This tooltip contains \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                            children: \"formatted text\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 31\n                                        }, void 0),\n                                        \" and multiple lines.\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 9\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: \"It can include any React content!\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 9\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 8\n                                }, void 0),\n                                maxWidth: 250,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Rich Content\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"This is a very long tooltip content that will wrap to multiple lines to demonstrate how the tooltip handles longer text content gracefully.\",\n                                maxWidth: 200,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Long Text\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 96,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Without Arrow\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            content: \"Tooltip without arrow\",\n                            showArrow: false,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                children: \"No Arrow\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 7\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 6\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 124,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Custom Delay\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Fast tooltip (100ms)\",\n                                delay: 100,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Fast\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Slow tooltip (1000ms)\",\n                                delay: 1000,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    children: \"Slow\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 134,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Disabled\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            content: \"This tooltip is disabled\",\n                            disabled: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_collective_core__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                children: \"Disabled Tooltip\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 7\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 6\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 148,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Different Trigger Elements\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"20px\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Tooltip on span\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        padding: \"8px\",\n                                        background: \"#f0f0f0\",\n                                        borderRadius: \"4px\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: \"Hover this span\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Tooltip__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                                content: \"Tooltip on image\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Crect width='40' height='40' fill='%23ddd'/%3E%3Ctext x='20' y='25' text-anchor='middle' fill='%23999'%3E\\uD83D\\uDCF7%3C/text%3E%3C/svg%3E\",\n                                    alt: \"Example\",\n                                    style: {\n                                        cursor: \"pointer\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 7\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n                lineNumber: 158,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\base\\\\common\\\\Tooltip\\\\TooltipExample.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, _this);\n};\n_c = TooltipExample;\nvar _c;\n$RefreshReg$(_c, \"TooltipExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/TooltipExample.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTooltip: function() { return /* binding */ useTooltip; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTooltip auto */ \n\nvar useTooltip = function() {\n    var _ref = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, _ref_placement = _ref.placement, placement = _ref_placement === void 0 ? \"top\" : _ref_placement, _ref_trigger = _ref.trigger, trigger = _ref_trigger === void 0 ? \"hover\" : _ref_trigger, _ref_delay = _ref.delay, delay = _ref_delay === void 0 ? 200 : _ref_delay, _ref_disabled = _ref.disabled, disabled = _ref_disabled === void 0 ? false : _ref_disabled;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__._)((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), 2), isVisible = _useState[0], setIsVisible = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_1__._)((0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        top: 0,\n        left: 0,\n        placement: placement\n    }), 2), position = _useState1[0], setPosition = _useState1[1];\n    var triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var calculatePosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (!triggerRef.current || !tooltipRef.current) return;\n        var triggerRect = triggerRef.current.getBoundingClientRect();\n        var tooltipRect = tooltipRef.current.getBoundingClientRect();\n        var viewport = {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n        var finalPlacement = placement;\n        var top = 0;\n        var left = 0;\n        // Calculate initial position based on placement\n        switch(placement){\n            case \"top\":\n                top = triggerRect.top - tooltipRect.height - 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"bottom\":\n                top = triggerRect.bottom + 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"left\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.left - tooltipRect.width - 8;\n                break;\n            case \"right\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.right + 8;\n                break;\n        }\n        // Auto-adjust if tooltip goes outside viewport\n        if (top < 8) {\n            finalPlacement = \"bottom\";\n            top = triggerRect.bottom + 8;\n        } else if (top + tooltipRect.height > viewport.height - 8) {\n            finalPlacement = \"top\";\n            top = triggerRect.top - tooltipRect.height - 8;\n        }\n        if (left < 8) {\n            left = 8;\n        } else if (left + tooltipRect.width > viewport.width - 8) {\n            left = viewport.width - tooltipRect.width - 8;\n        }\n        setPosition({\n            top: top,\n            left: left,\n            placement: finalPlacement\n        });\n    }, [\n        placement\n    ]);\n    var showTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (disabled) return;\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(function() {\n            setIsVisible(true);\n        }, delay);\n    }, [\n        delay,\n        disabled\n    ]);\n    var hideTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(function() {\n            setIsVisible(false);\n        }, delay);\n    }, [\n        delay\n    ]);\n    var handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"hover\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    var handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"hover\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    var handleClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"click\") {\n            if (isVisible) {\n                hideTooltip();\n            } else {\n                showTooltip();\n            }\n        }\n    }, [\n        trigger,\n        isVisible,\n        showTooltip,\n        hideTooltip\n    ]);\n    var handleFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"focus\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    var handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (trigger === \"focus\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    // Update position when tooltip becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (isVisible) {\n            calculatePosition();\n        }\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        var handleResize = function() {\n            if (isVisible) {\n                calculatePosition();\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        return function() {\n            return window.removeEventListener(\"resize\", handleResize);\n        };\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle click outside for click trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (trigger !== \"click\" || !isVisible) return;\n        var handleClickOutside = function(event) {\n            if (triggerRef.current && tooltipRef.current && !triggerRef.current.contains(event.target) && !tooltipRef.current.contains(event.target)) {\n                hideTooltip();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            return document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        trigger,\n        isVisible,\n        hideTooltip\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        return function() {\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n        };\n    }, []);\n    return {\n        isVisible: isVisible,\n        position: position,\n        triggerRef: triggerRef,\n        tooltipRef: tooltipRef,\n        triggerProps: {\n            onMouseEnter: handleMouseEnter,\n            onMouseLeave: handleMouseLeave,\n            onClick: handleClick,\n            onFocus: handleFocus,\n            onBlur: handleBlur\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\n"));

/***/ })

}]);