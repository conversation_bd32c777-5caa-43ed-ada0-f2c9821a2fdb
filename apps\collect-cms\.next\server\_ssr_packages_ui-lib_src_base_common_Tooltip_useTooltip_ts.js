"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_packages_ui-lib_src_base_common_Tooltip_useTooltip_ts";
exports.ids = ["_ssr_packages_ui-lib_src_base_common_Tooltip_useTooltip_ts"];
exports.modules = {

/***/ "(ssr)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts":
/*!*******************************************************************!*\
  !*** ../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTooltip: () => (/* binding */ useTooltip)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTooltip auto */ \nconst useTooltip = ({ placement = \"top\", trigger = \"hover\", delay = 200, disabled = false } = {})=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        top: 0,\n        left: 0,\n        placement\n    });\n    const triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const calculatePosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!triggerRef.current || !tooltipRef.current) return;\n        const triggerRect = triggerRef.current.getBoundingClientRect();\n        const tooltipRect = tooltipRef.current.getBoundingClientRect();\n        const viewport = {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n        let finalPlacement = placement;\n        let top = 0;\n        let left = 0;\n        // Calculate initial position based on placement\n        switch(placement){\n            case \"top\":\n                top = triggerRect.top - tooltipRect.height - 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"bottom\":\n                top = triggerRect.bottom + 8;\n                left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;\n                break;\n            case \"left\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.left - tooltipRect.width - 8;\n                break;\n            case \"right\":\n                top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;\n                left = triggerRect.right + 8;\n                break;\n        }\n        // Auto-adjust if tooltip goes outside viewport\n        if (top < 8) {\n            finalPlacement = \"bottom\";\n            top = triggerRect.bottom + 8;\n        } else if (top + tooltipRect.height > viewport.height - 8) {\n            finalPlacement = \"top\";\n            top = triggerRect.top - tooltipRect.height - 8;\n        }\n        if (left < 8) {\n            left = 8;\n        } else if (left + tooltipRect.width > viewport.width - 8) {\n            left = viewport.width - tooltipRect.width - 8;\n        }\n        setPosition({\n            top,\n            left,\n            placement: finalPlacement\n        });\n    }, [\n        placement\n    ]);\n    const showTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (disabled) return;\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(()=>{\n            setIsVisible(true);\n        }, delay);\n    }, [\n        delay,\n        disabled\n    ]);\n    const hideTooltip = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        timeoutRef.current = setTimeout(()=>{\n            setIsVisible(false);\n        }, delay);\n    }, [\n        delay\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"hover\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"hover\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"click\") {\n            if (isVisible) {\n                hideTooltip();\n            } else {\n                showTooltip();\n            }\n        }\n    }, [\n        trigger,\n        isVisible,\n        showTooltip,\n        hideTooltip\n    ]);\n    const handleFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"focus\") {\n            showTooltip();\n        }\n    }, [\n        trigger,\n        showTooltip\n    ]);\n    const handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (trigger === \"focus\") {\n            hideTooltip();\n        }\n    }, [\n        trigger,\n        hideTooltip\n    ]);\n    // Update position when tooltip becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (isVisible) {\n            calculatePosition();\n        }\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (isVisible) {\n                calculatePosition();\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, [\n        isVisible,\n        calculatePosition\n    ]);\n    // Handle click outside for click trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (trigger !== \"click\" || !isVisible) return;\n        const handleClickOutside = (event)=>{\n            if (triggerRef.current && tooltipRef.current && !triggerRef.current.contains(event.target) && !tooltipRef.current.contains(event.target)) {\n                hideTooltip();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, [\n        trigger,\n        isVisible,\n        hideTooltip\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n        };\n    }, []);\n    return {\n        isVisible,\n        position,\n        triggerRef,\n        tooltipRef,\n        triggerProps: {\n            onMouseEnter: handleMouseEnter,\n            onMouseLeave: handleMouseLeave,\n            onClick: handleClick,\n            onFocus: handleFocus,\n            onBlur: handleBlur\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9iYXNlL2NvbW1vbi9Ub29sdGlwL3VzZVRvb2x0aXAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O2dFQUVnRTtBQWtCekQsTUFBTUksYUFBYSxDQUFDLEVBQzFCQyxZQUFZLEtBQUssRUFDakJDLFVBQVUsT0FBTyxFQUNqQkMsUUFBUSxHQUFHLEVBQ1hDLFdBQVcsS0FBSyxFQUNDLEdBQUcsQ0FBQyxDQUFDO0lBQ3RCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNRLFVBQVVDLFlBQVksR0FBR1QsK0NBQVFBLENBQWtCO1FBQUVVLEtBQUs7UUFBR0MsTUFBTTtRQUFHVDtJQUFVO0lBQ3ZGLE1BQU1VLGFBQWFiLDZDQUFNQSxDQUFjO0lBQ3ZDLE1BQU1jLGFBQWFkLDZDQUFNQSxDQUFpQjtJQUMxQyxNQUFNZSxhQUFhZiw2Q0FBTUE7SUFFekIsTUFBTWdCLG9CQUFvQmxCLGtEQUFXQSxDQUFDO1FBQ3JDLElBQUksQ0FBQ2UsV0FBV0ksT0FBTyxJQUFJLENBQUNILFdBQVdHLE9BQU8sRUFBRTtRQUVoRCxNQUFNQyxjQUFjTCxXQUFXSSxPQUFPLENBQUNFLHFCQUFxQjtRQUM1RCxNQUFNQyxjQUFjTixXQUFXRyxPQUFPLENBQUNFLHFCQUFxQjtRQUM1RCxNQUFNRSxXQUFXO1lBQ2hCQyxPQUFPQyxPQUFPQyxVQUFVO1lBQ3hCQyxRQUFRRixPQUFPRyxXQUFXO1FBQzNCO1FBRUEsSUFBSUMsaUJBQWlCeEI7UUFDckIsSUFBSVEsTUFBTTtRQUNWLElBQUlDLE9BQU87UUFFWCxnREFBZ0Q7UUFDaEQsT0FBUVQ7WUFDUCxLQUFLO2dCQUNKUSxNQUFNTyxZQUFZUCxHQUFHLEdBQUdTLFlBQVlLLE1BQU0sR0FBRztnQkFDN0NiLE9BQU9NLFlBQVlOLElBQUksR0FBR00sWUFBWUksS0FBSyxHQUFHLElBQUlGLFlBQVlFLEtBQUssR0FBRztnQkFDdEU7WUFDRCxLQUFLO2dCQUNKWCxNQUFNTyxZQUFZVSxNQUFNLEdBQUc7Z0JBQzNCaEIsT0FBT00sWUFBWU4sSUFBSSxHQUFHTSxZQUFZSSxLQUFLLEdBQUcsSUFBSUYsWUFBWUUsS0FBSyxHQUFHO2dCQUN0RTtZQUNELEtBQUs7Z0JBQ0pYLE1BQU1PLFlBQVlQLEdBQUcsR0FBR08sWUFBWU8sTUFBTSxHQUFHLElBQUlMLFlBQVlLLE1BQU0sR0FBRztnQkFDdEViLE9BQU9NLFlBQVlOLElBQUksR0FBR1EsWUFBWUUsS0FBSyxHQUFHO2dCQUM5QztZQUNELEtBQUs7Z0JBQ0pYLE1BQU1PLFlBQVlQLEdBQUcsR0FBR08sWUFBWU8sTUFBTSxHQUFHLElBQUlMLFlBQVlLLE1BQU0sR0FBRztnQkFDdEViLE9BQU9NLFlBQVlXLEtBQUssR0FBRztnQkFDM0I7UUFDRjtRQUVBLCtDQUErQztRQUMvQyxJQUFJbEIsTUFBTSxHQUFHO1lBQ1pnQixpQkFBaUI7WUFDakJoQixNQUFNTyxZQUFZVSxNQUFNLEdBQUc7UUFDNUIsT0FBTyxJQUFJakIsTUFBTVMsWUFBWUssTUFBTSxHQUFHSixTQUFTSSxNQUFNLEdBQUcsR0FBRztZQUMxREUsaUJBQWlCO1lBQ2pCaEIsTUFBTU8sWUFBWVAsR0FBRyxHQUFHUyxZQUFZSyxNQUFNLEdBQUc7UUFDOUM7UUFFQSxJQUFJYixPQUFPLEdBQUc7WUFDYkEsT0FBTztRQUNSLE9BQU8sSUFBSUEsT0FBT1EsWUFBWUUsS0FBSyxHQUFHRCxTQUFTQyxLQUFLLEdBQUcsR0FBRztZQUN6RFYsT0FBT1MsU0FBU0MsS0FBSyxHQUFHRixZQUFZRSxLQUFLLEdBQUc7UUFDN0M7UUFFQVosWUFBWTtZQUFFQztZQUFLQztZQUFNVCxXQUFXd0I7UUFBZTtJQUNwRCxHQUFHO1FBQUN4QjtLQUFVO0lBRWQsTUFBTTJCLGNBQWNoQyxrREFBV0EsQ0FBQztRQUMvQixJQUFJUSxVQUFVO1FBRWQsSUFBSVMsV0FBV0UsT0FBTyxFQUFFO1lBQ3ZCYyxhQUFhaEIsV0FBV0UsT0FBTztRQUNoQztRQUVBRixXQUFXRSxPQUFPLEdBQUdlLFdBQVc7WUFDL0J4QixhQUFhO1FBQ2QsR0FBR0g7SUFDSixHQUFHO1FBQUNBO1FBQU9DO0tBQVM7SUFFcEIsTUFBTTJCLGNBQWNuQyxrREFBV0EsQ0FBQztRQUMvQixJQUFJaUIsV0FBV0UsT0FBTyxFQUFFO1lBQ3ZCYyxhQUFhaEIsV0FBV0UsT0FBTztRQUNoQztRQUVBRixXQUFXRSxPQUFPLEdBQUdlLFdBQVc7WUFDL0J4QixhQUFhO1FBQ2QsR0FBR0g7SUFDSixHQUFHO1FBQUNBO0tBQU07SUFFVixNQUFNNkIsbUJBQW1CcEMsa0RBQVdBLENBQUM7UUFDcEMsSUFBSU0sWUFBWSxTQUFTO1lBQ3hCMEI7UUFDRDtJQUNELEdBQUc7UUFBQzFCO1FBQVMwQjtLQUFZO0lBRXpCLE1BQU1LLG1CQUFtQnJDLGtEQUFXQSxDQUFDO1FBQ3BDLElBQUlNLFlBQVksU0FBUztZQUN4QjZCO1FBQ0Q7SUFDRCxHQUFHO1FBQUM3QjtRQUFTNkI7S0FBWTtJQUV6QixNQUFNRyxjQUFjdEMsa0RBQVdBLENBQUM7UUFDL0IsSUFBSU0sWUFBWSxTQUFTO1lBQ3hCLElBQUlHLFdBQVc7Z0JBQ2QwQjtZQUNELE9BQU87Z0JBQ05IO1lBQ0Q7UUFDRDtJQUNELEdBQUc7UUFBQzFCO1FBQVNHO1FBQVd1QjtRQUFhRztLQUFZO0lBRWpELE1BQU1JLGNBQWN2QyxrREFBV0EsQ0FBQztRQUMvQixJQUFJTSxZQUFZLFNBQVM7WUFDeEIwQjtRQUNEO0lBQ0QsR0FBRztRQUFDMUI7UUFBUzBCO0tBQVk7SUFFekIsTUFBTVEsYUFBYXhDLGtEQUFXQSxDQUFDO1FBQzlCLElBQUlNLFlBQVksU0FBUztZQUN4QjZCO1FBQ0Q7SUFDRCxHQUFHO1FBQUM3QjtRQUFTNkI7S0FBWTtJQUV6QiwrQ0FBK0M7SUFDL0NsQyxnREFBU0EsQ0FBQztRQUNULElBQUlRLFdBQVc7WUFDZFM7UUFDRDtJQUNELEdBQUc7UUFBQ1Q7UUFBV1M7S0FBa0I7SUFFakMsdUJBQXVCO0lBQ3ZCakIsZ0RBQVNBLENBQUM7UUFDVCxNQUFNd0MsZUFBZTtZQUNwQixJQUFJaEMsV0FBVztnQkFDZFM7WUFDRDtRQUNEO1FBRUFPLE9BQU9pQixnQkFBZ0IsQ0FBQyxVQUFVRDtRQUNsQyxPQUFPLElBQU1oQixPQUFPa0IsbUJBQW1CLENBQUMsVUFBVUY7SUFDbkQsR0FBRztRQUFDaEM7UUFBV1M7S0FBa0I7SUFFakMseUNBQXlDO0lBQ3pDakIsZ0RBQVNBLENBQUM7UUFDVCxJQUFJSyxZQUFZLFdBQVcsQ0FBQ0csV0FBVztRQUV2QyxNQUFNbUMscUJBQXFCLENBQUNDO1lBQzNCLElBQ0M5QixXQUFXSSxPQUFPLElBQ2xCSCxXQUFXRyxPQUFPLElBQ2xCLENBQUNKLFdBQVdJLE9BQU8sQ0FBQzJCLFFBQVEsQ0FBQ0QsTUFBTUUsTUFBTSxLQUN6QyxDQUFDL0IsV0FBV0csT0FBTyxDQUFDMkIsUUFBUSxDQUFDRCxNQUFNRSxNQUFNLEdBQ3hDO2dCQUNEWjtZQUNEO1FBQ0Q7UUFFQWEsU0FBU04sZ0JBQWdCLENBQUMsYUFBYUU7UUFDdkMsT0FBTyxJQUFNSSxTQUFTTCxtQkFBbUIsQ0FBQyxhQUFhQztJQUN4RCxHQUFHO1FBQUN0QztRQUFTRztRQUFXMEI7S0FBWTtJQUVwQyw2QkFBNkI7SUFDN0JsQyxnREFBU0EsQ0FBQztRQUNULE9BQU87WUFDTixJQUFJZ0IsV0FBV0UsT0FBTyxFQUFFO2dCQUN2QmMsYUFBYWhCLFdBQVdFLE9BQU87WUFDaEM7UUFDRDtJQUNELEdBQUcsRUFBRTtJQUVMLE9BQU87UUFDTlY7UUFDQUU7UUFDQUk7UUFDQUM7UUFDQWlDLGNBQWM7WUFDYkMsY0FBY2Q7WUFDZGUsY0FBY2Q7WUFDZGUsU0FBU2Q7WUFDVGUsU0FBU2Q7WUFDVGUsUUFBUWQ7UUFDVDtJQUNEO0FBQ0QsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL3VpLWxpYi9zcmMvYmFzZS9jb21tb24vVG9vbHRpcC91c2VUb29sdGlwLnRzP2E2OTQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcblxuZXhwb3J0IHR5cGUgVG9vbHRpcFBsYWNlbWVudCA9ICd0b3AnIHwgJ2JvdHRvbScgfCAnbGVmdCcgfCAncmlnaHQnXG5leHBvcnQgdHlwZSBUb29sdGlwVHJpZ2dlciA9ICdob3ZlcicgfCAnY2xpY2snIHwgJ2ZvY3VzJ1xuXG5pbnRlcmZhY2UgVXNlVG9vbHRpcFByb3BzIHtcblx0cGxhY2VtZW50PzogVG9vbHRpcFBsYWNlbWVudFxuXHR0cmlnZ2VyPzogVG9vbHRpcFRyaWdnZXJcblx0ZGVsYXk/OiBudW1iZXJcblx0ZGlzYWJsZWQ/OiBib29sZWFuXG59XG5cbmludGVyZmFjZSBUb29sdGlwUG9zaXRpb24ge1xuXHR0b3A6IG51bWJlclxuXHRsZWZ0OiBudW1iZXJcblx0cGxhY2VtZW50OiBUb29sdGlwUGxhY2VtZW50XG59XG5cbmV4cG9ydCBjb25zdCB1c2VUb29sdGlwID0gKHtcblx0cGxhY2VtZW50ID0gJ3RvcCcsXG5cdHRyaWdnZXIgPSAnaG92ZXInLFxuXHRkZWxheSA9IDIwMCxcblx0ZGlzYWJsZWQgPSBmYWxzZSxcbn06IFVzZVRvb2x0aXBQcm9wcyA9IHt9KSA9PiB7XG5cdGNvbnN0IFtpc1Zpc2libGUsIHNldElzVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSlcblx0Y29uc3QgW3Bvc2l0aW9uLCBzZXRQb3NpdGlvbl0gPSB1c2VTdGF0ZTxUb29sdGlwUG9zaXRpb24+KHsgdG9wOiAwLCBsZWZ0OiAwLCBwbGFjZW1lbnQgfSlcblx0Y29uc3QgdHJpZ2dlclJlZiA9IHVzZVJlZjxIVE1MRWxlbWVudD4obnVsbClcblx0Y29uc3QgdG9vbHRpcFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbClcblx0Y29uc3QgdGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dD4oKVxuXG5cdGNvbnN0IGNhbGN1bGF0ZVBvc2l0aW9uID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuXHRcdGlmICghdHJpZ2dlclJlZi5jdXJyZW50IHx8ICF0b29sdGlwUmVmLmN1cnJlbnQpIHJldHVyblxuXG5cdFx0Y29uc3QgdHJpZ2dlclJlY3QgPSB0cmlnZ2VyUmVmLmN1cnJlbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KClcblx0XHRjb25zdCB0b29sdGlwUmVjdCA9IHRvb2x0aXBSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKVxuXHRcdGNvbnN0IHZpZXdwb3J0ID0ge1xuXHRcdFx0d2lkdGg6IHdpbmRvdy5pbm5lcldpZHRoLFxuXHRcdFx0aGVpZ2h0OiB3aW5kb3cuaW5uZXJIZWlnaHQsXG5cdFx0fVxuXG5cdFx0bGV0IGZpbmFsUGxhY2VtZW50ID0gcGxhY2VtZW50XG5cdFx0bGV0IHRvcCA9IDBcblx0XHRsZXQgbGVmdCA9IDBcblxuXHRcdC8vIENhbGN1bGF0ZSBpbml0aWFsIHBvc2l0aW9uIGJhc2VkIG9uIHBsYWNlbWVudFxuXHRcdHN3aXRjaCAocGxhY2VtZW50KSB7XG5cdFx0XHRjYXNlICd0b3AnOlxuXHRcdFx0XHR0b3AgPSB0cmlnZ2VyUmVjdC50b3AgLSB0b29sdGlwUmVjdC5oZWlnaHQgLSA4XG5cdFx0XHRcdGxlZnQgPSB0cmlnZ2VyUmVjdC5sZWZ0ICsgdHJpZ2dlclJlY3Qud2lkdGggLyAyIC0gdG9vbHRpcFJlY3Qud2lkdGggLyAyXG5cdFx0XHRcdGJyZWFrXG5cdFx0XHRjYXNlICdib3R0b20nOlxuXHRcdFx0XHR0b3AgPSB0cmlnZ2VyUmVjdC5ib3R0b20gKyA4XG5cdFx0XHRcdGxlZnQgPSB0cmlnZ2VyUmVjdC5sZWZ0ICsgdHJpZ2dlclJlY3Qud2lkdGggLyAyIC0gdG9vbHRpcFJlY3Qud2lkdGggLyAyXG5cdFx0XHRcdGJyZWFrXG5cdFx0XHRjYXNlICdsZWZ0Jzpcblx0XHRcdFx0dG9wID0gdHJpZ2dlclJlY3QudG9wICsgdHJpZ2dlclJlY3QuaGVpZ2h0IC8gMiAtIHRvb2x0aXBSZWN0LmhlaWdodCAvIDJcblx0XHRcdFx0bGVmdCA9IHRyaWdnZXJSZWN0LmxlZnQgLSB0b29sdGlwUmVjdC53aWR0aCAtIDhcblx0XHRcdFx0YnJlYWtcblx0XHRcdGNhc2UgJ3JpZ2h0Jzpcblx0XHRcdFx0dG9wID0gdHJpZ2dlclJlY3QudG9wICsgdHJpZ2dlclJlY3QuaGVpZ2h0IC8gMiAtIHRvb2x0aXBSZWN0LmhlaWdodCAvIDJcblx0XHRcdFx0bGVmdCA9IHRyaWdnZXJSZWN0LnJpZ2h0ICsgOFxuXHRcdFx0XHRicmVha1xuXHRcdH1cblxuXHRcdC8vIEF1dG8tYWRqdXN0IGlmIHRvb2x0aXAgZ29lcyBvdXRzaWRlIHZpZXdwb3J0XG5cdFx0aWYgKHRvcCA8IDgpIHtcblx0XHRcdGZpbmFsUGxhY2VtZW50ID0gJ2JvdHRvbSdcblx0XHRcdHRvcCA9IHRyaWdnZXJSZWN0LmJvdHRvbSArIDhcblx0XHR9IGVsc2UgaWYgKHRvcCArIHRvb2x0aXBSZWN0LmhlaWdodCA+IHZpZXdwb3J0LmhlaWdodCAtIDgpIHtcblx0XHRcdGZpbmFsUGxhY2VtZW50ID0gJ3RvcCdcblx0XHRcdHRvcCA9IHRyaWdnZXJSZWN0LnRvcCAtIHRvb2x0aXBSZWN0LmhlaWdodCAtIDhcblx0XHR9XG5cblx0XHRpZiAobGVmdCA8IDgpIHtcblx0XHRcdGxlZnQgPSA4XG5cdFx0fSBlbHNlIGlmIChsZWZ0ICsgdG9vbHRpcFJlY3Qud2lkdGggPiB2aWV3cG9ydC53aWR0aCAtIDgpIHtcblx0XHRcdGxlZnQgPSB2aWV3cG9ydC53aWR0aCAtIHRvb2x0aXBSZWN0LndpZHRoIC0gOFxuXHRcdH1cblxuXHRcdHNldFBvc2l0aW9uKHsgdG9wLCBsZWZ0LCBwbGFjZW1lbnQ6IGZpbmFsUGxhY2VtZW50IH0pXG5cdH0sIFtwbGFjZW1lbnRdKVxuXG5cdGNvbnN0IHNob3dUb29sdGlwID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuXHRcdGlmIChkaXNhYmxlZCkgcmV0dXJuXG5cdFx0XG5cdFx0aWYgKHRpbWVvdXRSZWYuY3VycmVudCkge1xuXHRcdFx0Y2xlYXJUaW1lb3V0KHRpbWVvdXRSZWYuY3VycmVudClcblx0XHR9XG5cblx0XHR0aW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcblx0XHRcdHNldElzVmlzaWJsZSh0cnVlKVxuXHRcdH0sIGRlbGF5KVxuXHR9LCBbZGVsYXksIGRpc2FibGVkXSlcblxuXHRjb25zdCBoaWRlVG9vbHRpcCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcblx0XHRpZiAodGltZW91dFJlZi5jdXJyZW50KSB7XG5cdFx0XHRjbGVhclRpbWVvdXQodGltZW91dFJlZi5jdXJyZW50KVxuXHRcdH1cblxuXHRcdHRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuXHRcdFx0c2V0SXNWaXNpYmxlKGZhbHNlKVxuXHRcdH0sIGRlbGF5KVxuXHR9LCBbZGVsYXldKVxuXG5cdGNvbnN0IGhhbmRsZU1vdXNlRW50ZXIgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG5cdFx0aWYgKHRyaWdnZXIgPT09ICdob3ZlcicpIHtcblx0XHRcdHNob3dUb29sdGlwKClcblx0XHR9XG5cdH0sIFt0cmlnZ2VyLCBzaG93VG9vbHRpcF0pXG5cblx0Y29uc3QgaGFuZGxlTW91c2VMZWF2ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcblx0XHRpZiAodHJpZ2dlciA9PT0gJ2hvdmVyJykge1xuXHRcdFx0aGlkZVRvb2x0aXAoKVxuXHRcdH1cblx0fSwgW3RyaWdnZXIsIGhpZGVUb29sdGlwXSlcblxuXHRjb25zdCBoYW5kbGVDbGljayA9IHVzZUNhbGxiYWNrKCgpID0+IHtcblx0XHRpZiAodHJpZ2dlciA9PT0gJ2NsaWNrJykge1xuXHRcdFx0aWYgKGlzVmlzaWJsZSkge1xuXHRcdFx0XHRoaWRlVG9vbHRpcCgpXG5cdFx0XHR9IGVsc2Uge1xuXHRcdFx0XHRzaG93VG9vbHRpcCgpXG5cdFx0XHR9XG5cdFx0fVxuXHR9LCBbdHJpZ2dlciwgaXNWaXNpYmxlLCBzaG93VG9vbHRpcCwgaGlkZVRvb2x0aXBdKVxuXG5cdGNvbnN0IGhhbmRsZUZvY3VzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuXHRcdGlmICh0cmlnZ2VyID09PSAnZm9jdXMnKSB7XG5cdFx0XHRzaG93VG9vbHRpcCgpXG5cdFx0fVxuXHR9LCBbdHJpZ2dlciwgc2hvd1Rvb2x0aXBdKVxuXG5cdGNvbnN0IGhhbmRsZUJsdXIgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG5cdFx0aWYgKHRyaWdnZXIgPT09ICdmb2N1cycpIHtcblx0XHRcdGhpZGVUb29sdGlwKClcblx0XHR9XG5cdH0sIFt0cmlnZ2VyLCBoaWRlVG9vbHRpcF0pXG5cblx0Ly8gVXBkYXRlIHBvc2l0aW9uIHdoZW4gdG9vbHRpcCBiZWNvbWVzIHZpc2libGVcblx0dXNlRWZmZWN0KCgpID0+IHtcblx0XHRpZiAoaXNWaXNpYmxlKSB7XG5cdFx0XHRjYWxjdWxhdGVQb3NpdGlvbigpXG5cdFx0fVxuXHR9LCBbaXNWaXNpYmxlLCBjYWxjdWxhdGVQb3NpdGlvbl0pXG5cblx0Ly8gSGFuZGxlIHdpbmRvdyByZXNpemVcblx0dXNlRWZmZWN0KCgpID0+IHtcblx0XHRjb25zdCBoYW5kbGVSZXNpemUgPSAoKSA9PiB7XG5cdFx0XHRpZiAoaXNWaXNpYmxlKSB7XG5cdFx0XHRcdGNhbGN1bGF0ZVBvc2l0aW9uKClcblx0XHRcdH1cblx0XHR9XG5cblx0XHR3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlUmVzaXplKVxuXHRcdHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlUmVzaXplKVxuXHR9LCBbaXNWaXNpYmxlLCBjYWxjdWxhdGVQb3NpdGlvbl0pXG5cblx0Ly8gSGFuZGxlIGNsaWNrIG91dHNpZGUgZm9yIGNsaWNrIHRyaWdnZXJcblx0dXNlRWZmZWN0KCgpID0+IHtcblx0XHRpZiAodHJpZ2dlciAhPT0gJ2NsaWNrJyB8fCAhaXNWaXNpYmxlKSByZXR1cm5cblxuXHRcdGNvbnN0IGhhbmRsZUNsaWNrT3V0c2lkZSA9IChldmVudDogTW91c2VFdmVudCkgPT4ge1xuXHRcdFx0aWYgKFxuXHRcdFx0XHR0cmlnZ2VyUmVmLmN1cnJlbnQgJiZcblx0XHRcdFx0dG9vbHRpcFJlZi5jdXJyZW50ICYmXG5cdFx0XHRcdCF0cmlnZ2VyUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpICYmXG5cdFx0XHRcdCF0b29sdGlwUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpXG5cdFx0XHQpIHtcblx0XHRcdFx0aGlkZVRvb2x0aXAoKVxuXHRcdFx0fVxuXHRcdH1cblxuXHRcdGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSlcblx0XHRyZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKVxuXHR9LCBbdHJpZ2dlciwgaXNWaXNpYmxlLCBoaWRlVG9vbHRpcF0pXG5cblx0Ly8gQ2xlYW51cCB0aW1lb3V0IG9uIHVubW91bnRcblx0dXNlRWZmZWN0KCgpID0+IHtcblx0XHRyZXR1cm4gKCkgPT4ge1xuXHRcdFx0aWYgKHRpbWVvdXRSZWYuY3VycmVudCkge1xuXHRcdFx0XHRjbGVhclRpbWVvdXQodGltZW91dFJlZi5jdXJyZW50KVxuXHRcdFx0fVxuXHRcdH1cblx0fSwgW10pXG5cblx0cmV0dXJuIHtcblx0XHRpc1Zpc2libGUsXG5cdFx0cG9zaXRpb24sXG5cdFx0dHJpZ2dlclJlZixcblx0XHR0b29sdGlwUmVmLFxuXHRcdHRyaWdnZXJQcm9wczoge1xuXHRcdFx0b25Nb3VzZUVudGVyOiBoYW5kbGVNb3VzZUVudGVyLFxuXHRcdFx0b25Nb3VzZUxlYXZlOiBoYW5kbGVNb3VzZUxlYXZlLFxuXHRcdFx0b25DbGljazogaGFuZGxlQ2xpY2ssXG5cdFx0XHRvbkZvY3VzOiBoYW5kbGVGb2N1cyxcblx0XHRcdG9uQmx1cjogaGFuZGxlQmx1cixcblx0XHR9LFxuXHR9XG59XG4iXSwibmFtZXMiOlsidXNlQ2FsbGJhY2siLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInVzZVRvb2x0aXAiLCJwbGFjZW1lbnQiLCJ0cmlnZ2VyIiwiZGVsYXkiLCJkaXNhYmxlZCIsImlzVmlzaWJsZSIsInNldElzVmlzaWJsZSIsInBvc2l0aW9uIiwic2V0UG9zaXRpb24iLCJ0b3AiLCJsZWZ0IiwidHJpZ2dlclJlZiIsInRvb2x0aXBSZWYiLCJ0aW1lb3V0UmVmIiwiY2FsY3VsYXRlUG9zaXRpb24iLCJjdXJyZW50IiwidHJpZ2dlclJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJ0b29sdGlwUmVjdCIsInZpZXdwb3J0Iiwid2lkdGgiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiaGVpZ2h0IiwiaW5uZXJIZWlnaHQiLCJmaW5hbFBsYWNlbWVudCIsImJvdHRvbSIsInJpZ2h0Iiwic2hvd1Rvb2x0aXAiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiaGlkZVRvb2x0aXAiLCJoYW5kbGVNb3VzZUVudGVyIiwiaGFuZGxlTW91c2VMZWF2ZSIsImhhbmRsZUNsaWNrIiwiaGFuZGxlRm9jdXMiLCJoYW5kbGVCbHVyIiwiaGFuZGxlUmVzaXplIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiZG9jdW1lbnQiLCJ0cmlnZ2VyUHJvcHMiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJvbkNsaWNrIiwib25Gb2N1cyIsIm9uQmx1ciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/base/common/Tooltip/useTooltip.ts\n");

/***/ })

};
;