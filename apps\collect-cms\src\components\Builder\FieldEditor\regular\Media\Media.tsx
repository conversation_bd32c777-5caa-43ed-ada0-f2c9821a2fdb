import { Button, Icon, Image, useIsomorphicLayoutEffect } from '@collective/core'
import type { IMediaProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import dayjs from 'dayjs'
import { usePathname } from 'next/navigation'
import { useState, useMemo, useContext, useId } from 'react'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import type { FieldProps } from '../../FieldEditor'
import styles from './media.module.scss'
import { MediaInfoLayer } from './MediaInfoLayer'

export const formatDate = (date: string) => dayjs(date).format('D/M/YYYY')
export const formatExt = (ext: string) => ext.replace('.', '')
export const checkArr = (value: unknown) => Array.isArray(value)

export type MediaToolType = {
	name: string
	icon: string
	action: string
	visible?: boolean
}

export interface MediaProps<T> extends FieldProps<T> {
	value?: T
	field?: string
	multiple?: boolean
	onChange: (props: { field: string; value: unknown }) => void
}

// Custom hook for shared media logic
export const useMediaHandlers = (
	propsValue: IMediaProps<'multiple' | 'single'>,
	setPropsValue: React.Dispatch<React.SetStateAction<IMediaProps<'multiple' | 'single'>>>,
	currentMediaIdx: number,
	setCurrentMediaIdx: React.Dispatch<React.SetStateAction<number>>,
	multiple?: boolean,
	onChange?: (props: { field: string; value: unknown }) => void,
	field?: string
) => {
	const context = useContext(PageBuilderContext)
	const { setMediaInfoData, setActiveMediaId } = context

	// Utility functions
	const createFileInput = (multiple: boolean = false) => {
		const input = document.createElement('input')
		input.type = 'file'
		input.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx'
		input.multiple = multiple
		return input
	}

	const validateFileSize = (file: File): { isValid: boolean; errorMessage?: string } => {
		const maxSize = 20 * 1024 * 1024 // 20MB
		const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2)

		if (file.size > maxSize) {
			return {
				isValid: false,
				errorMessage: `File "${file.name}" (${fileSizeMB}MB) exceeds the 20MB limit.`,
			}
		}

		return { isValid: true }
	}

	const fileToMediaProps = async (file: File): Promise<IMediaProps> => {
		const url = URL.createObjectURL(file)

		// Get image dimensions for image files
		let width: number | undefined
		let height: number | undefined

		if (file.type.startsWith('image/')) {
			try {
				const dimensions = await getImageDimensions(url)
				width = dimensions.width
				height = dimensions.height
			} catch (error) {
				console.warn('Failed to get image dimensions:', error)
				// Default dimensions for images if we can't read them
				width = 800
				height = 600
			}
		}

		return {
			id: Date.now() + Math.random(),
			name: file.name,
			url,
			ext: '.' + file.name.split('.').pop()?.toLowerCase(),
			mime: file.type,
			size: file.size,
			alternativeText: file.name,
			width,
			height,
		} as IMediaProps
	}

	const getImageDimensions = (url: string): Promise<{ width: number; height: number }> => {
		return new Promise((resolve, reject) => {
			const img = document.createElement('img')
			img.onload = () => {
				resolve({ width: img.naturalWidth, height: img.naturalHeight })
			}
			img.onerror = reject
			img.src = url
		})
	}

	const updatePropsValue = (newValue: IMediaProps<'multiple' | 'single'>) => {
		setPropsValue(newValue)
		// if (onChange) {
		// 	onChange({ field: field || 'media', value: newValue as unknown })
		// }
	}

	const handleNextMedia = () => {
		if (checkArr(propsValue) && propsValue.length > 0) {
			setCurrentMediaIdx((prevIdx) => (prevIdx + 1 < propsValue.length ? prevIdx + 1 : 0))
		}
	}

	const handlePrevMedia = () => {
		if (checkArr(propsValue) && propsValue.length > 0) {
			setCurrentMediaIdx((prevIdx) => (prevIdx - 1 >= 0 ? prevIdx - 1 : propsValue.length - 1))
		}
	}

	const handleAdd = () => {
		const input = createFileInput(multiple)
		input.onchange = async (e) => {
			const files = (e.target as HTMLInputElement).files
			if (!files) return

			const validFiles: File[] = []
			const invalidFiles: string[] = []

			// Validate each file
			Array.from(files).forEach((file) => {
				const validation = validateFileSize(file)
				if (validation.isValid) {
					validFiles.push(file)
				} else {
					invalidFiles.push(validation.errorMessage || `File ${file.name} is invalid`)
				}
			})

			// Show error messages for invalid files
			if (invalidFiles.length > 0) {
				alert(`The following files were skipped:\n\n${invalidFiles.join('\n')}`)
			}

			// Process valid files
			if (validFiles.length > 0) {
				const newMediaItems = await Promise.all(validFiles.map(fileToMediaProps))

				if (multiple) {
					const currentArray = checkArr(propsValue) ? propsValue : []
					const newValue = [...currentArray, ...newMediaItems] as IMediaProps<'multiple'>
					updatePropsValue(newValue)
				} else {
					updatePropsValue(newMediaItems[0] as IMediaProps<'single'>)
				}
			}
		}
		input.click()
	}

	const handleReplace = () => {
		const input = createFileInput(false)
		input.onchange = async (e) => {
			const files = (e.target as HTMLInputElement).files
			if (!files || !files[0]) return

			const validation = validateFileSize(files[0])
			if (!validation.isValid) {
				alert(validation.errorMessage || 'File exceeds the 20MB limit.')
				return
			}

			const newMedia = await fileToMediaProps(files[0])

			if (multiple && checkArr(propsValue)) {
				const newArray = [...propsValue]
				newArray[currentMediaIdx] = newMedia
				updatePropsValue(newArray as IMediaProps<'multiple'>)
			} else {
				updatePropsValue(newMedia as IMediaProps<'single'>)
			}

			// Update context
			setMediaInfoData(newMedia as unknown as IMediaProps<'single'>)
		}
		input.click()
	}

	const handleDuplicate = () => {
		const currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue
		if (!currentMedia) return

		const duplicatedMedia = {
			...currentMedia,
			id: Date.now() + Math.random(),
			name: `The copy of ${currentMedia.name}`,
		}

		if (multiple && checkArr(propsValue)) {
			const newArray = [...propsValue]
			newArray.splice(currentMediaIdx + 1, 0, duplicatedMedia)
			updatePropsValue(newArray as IMediaProps<'multiple'>)
		} else {
			// For single mode, replace current with duplicate
			updatePropsValue(duplicatedMedia as IMediaProps<'single'>)
		}
	}

	const handleRemove = () => {
		if (multiple && checkArr(propsValue)) {
			const newArray = [...propsValue]
			newArray.splice(currentMediaIdx, 1)

			// Adjust current index if needed
			if (currentMediaIdx >= newArray.length && newArray.length > 0) {
				setCurrentMediaIdx(newArray.length - 1)
			}

			// Only close MediaInfoLayer if no media left
			if (newArray.length === 0) {
				setActiveMediaId(null)
				setMediaInfoData({ name: '', url: '' })
			} else {
				// Update mediaInfoData to the new current media
				const newCurrentIdx =
					currentMediaIdx >= newArray.length ? newArray.length - 1 : currentMediaIdx
				setMediaInfoData(newArray[newCurrentIdx] as unknown as IMediaProps<'single'>)
			}

			updatePropsValue(newArray as IMediaProps<'multiple'>)
		} else {
			// For single mode, clear the media and close MediaInfoLayer
			setActiveMediaId(null)
			setMediaInfoData({ name: '', url: '' })
			updatePropsValue(null as unknown as IMediaProps<'single'>)
		}
	}

	const handleDownload = () => {
		const currentMedia = checkArr(propsValue) ? propsValue[currentMediaIdx] : propsValue
		if (!currentMedia?.url) return

		const link = document.createElement('a')
		link.href = currentMedia.url
		link.download = currentMedia.name || 'download'
		link.target = '_blank'
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
	}

	const handleAction = (key: string) => {
		switch (key) {
			case 'add':
				handleAdd()
				break
			case 'replace':
				handleReplace()
				break
			case 'duplicate':
				handleDuplicate()
				break
			case 'remove':
				handleRemove()
				break
			case 'download':
				handleDownload()
				break
			default:
				break
		}
	}

	return {
		handleAdd,
		handleReplace,
		handleDuplicate,
		handleRemove,
		handleDownload,
		handleAction,
		handleNextMedia,
		handlePrevMedia,
	}
}

export const Media = <T,>(props: MediaProps<T>) => {
	const { value, onChange, multiple } = props ?? {}
	const pathname = usePathname()
	const context = useContext(PageBuilderContext)
	const {
		mediaInfoData,
		setMediaInfoData,
		activeMediaId,
		setActiveMediaId,
		layerPos,
		setLayerPos,
		expandedSidebar,
		setChildComponentData,
	} = context
	const mediaId = useId()
	const [isEdit, setisEdit] = useState(false)
	const [propsValue, setPropsValue] = useState<IMediaProps<'multiple' | 'single'>>(
		multiple ? (value as IMediaProps<'multiple'>) : (value as IMediaProps<'single'>)
	)
	const [currentMedia, setCurrentMedia] = useState<IMediaProps>(
		(checkArr(propsValue) ? propsValue[0] : propsValue) as IMediaProps
	)
	const [currentMediaIdx, setCurrentMediaIdx] = useState(0)

	// Use shared media handlers
	const {
		handleAdd,
		handleReplace,
		handleDuplicate,
		handleRemove,
		handleDownload,
		handleAction,
		handleNextMedia,
		handlePrevMedia,
	} = useMediaHandlers(
		propsValue,
		setPropsValue,
		currentMediaIdx,
		setCurrentMediaIdx,
		multiple,
		onChange,
		props.field
	)

	useIsomorphicLayoutEffect(() => {
		if (checkArr(propsValue)) {
			setCurrentMedia(propsValue[currentMediaIdx] as IMediaProps)
			setMediaInfoData(propsValue[currentMediaIdx] as IMediaProps)
		} else {
			setCurrentMedia(propsValue as IMediaProps)
			setMediaInfoData(propsValue as IMediaProps)
		}
	}, [currentMediaIdx, propsValue])

	// useIsomorphicLayoutEffect(() => {
	// 	if (isEdit && currentMedia) {
	// 		handleEdit()
	// 	}
	// }, [currentMedia])

	useIsomorphicLayoutEffect(() => {
		mediaInfoData && mediaInfoData.name === '' && setisEdit(false)
	}, [mediaInfoData])

	useIsomorphicLayoutEffect(() => {
		setisEdit(activeMediaId === mediaId)
	}, [activeMediaId, mediaId])

	const mediaToolbar: MediaToolType[] = [
		{
			name: 'Add',
			icon: 'add',
			action: 'add',
			visible: !multiple,
		},
		{
			name: 'Replace',
			icon: 'replace',
			action: 'replace',
		},
		{
			name: 'Duplicate',
			icon: 'duplicate',
			action: 'duplicate',
			visible: !multiple,
		},
		{
			name: 'Remove',
			icon: 'remove',
			action: 'remove',
		},
		{
			name: 'Download',
			icon: 'download',
			action: 'download',
			visible: !isEdit,
		},
	]
	const filteredMediaToolbar = mediaToolbar.filter((tool) => !tool.visible)

	const handleEdit = () => {
		// Close LayerSidebarLayout when opening MediaInfoLayer
		setChildComponentData([])

		setMediaInfoData(currentMedia as unknown as IMediaProps<'single'>)
		setActiveMediaId(mediaId)
		setLayerPos(props.layerPos as string)
	}

	const handleBack = () => {
		setActiveMediaId(null)
		setMediaInfoData({ name: '', url: '' })
	}

	const isBuilderMode = useMemo(() => pathname?.startsWith('/content-builder/'), [pathname])

	useIsomorphicLayoutEffect(() => {
		if (
			(layerPos === 'left' && !expandedSidebar.left) ||
			(layerPos === 'right' && !expandedSidebar.right)
		) {
			setActiveMediaId(null)
			setMediaInfoData({ name: '', url: '' })
		}
	}, [expandedSidebar])

	return (
		<div className={styles.wrapper}>
			<div
				className={styles.controller}
				style={
					{
						'--controller-cols': isBuilderMode ? 12 : 8,
					} as React.CSSProperties
				}
			>
				{multiple && !isEdit && (
					<div className={styles.nav}>
						<Button className={styles.nav__btn} onClick={handlePrevMedia}>
							<Icon type="cms" variant="chevron-left" />
						</Button>
						<span
							className={styles.nav__index}
						>{`${currentMediaIdx + 1}/${checkArr(propsValue) ? propsValue.length : 0}`}</span>
						<Button className={styles.nav__btn} onClick={handleNextMedia}>
							<Icon type="cms" variant="chevron-right" />
						</Button>
					</div>
				)}
				<div
					className={cn(
						styles.body,
						!isBuilderMode && isEdit ? (multiple ? styles.detailed__multi : styles.detailed) : ''
					)}
				>
					{currentMedia ? (
						<div
							className={styles.item}
							style={
								{
									'--height': isBuilderMode ? '160px' : '280px',
								} as React.CSSProperties
							}
						>
							<span className={styles.tag}>{formatExt(currentMedia?.ext || '')}</span>
							<div className={styles.thumbnail}>
								<Image media={currentMedia as unknown as IMediaProps} alt="" />
							</div>
							{!isEdit && (
								<div className={styles.mask} title="Edit this media">
									<Button onClick={() => handleEdit()}>
										<Icon type="cms" variant="edit" />
									</Button>
								</div>
							)}
						</div>
					) : (
						<div
							className={styles.empty}
							style={
								{
									'--height': isBuilderMode ? '160px' : '280px',
								} as React.CSSProperties
							}
							title="Browse file(s)"
						>
							<Icon type="cms" variant="image" />
							<p>
								Drop your file(s) here or{' '}
								<Button onClick={() => handleAction('add')}>browse</Button>
							</p>
							<small>Max. File Size: 20MB</small>
						</div>
					)}
					{!isBuilderMode && isEdit && checkArr(propsValue) && (
						<div className={styles.items}>
							<button className={styles.items__nav} onClick={handlePrevMedia}>
								<Icon type="cms" variant="chevron-left" />
							</button>
							<div className={styles.items__list}>
								{propsValue.map((media, idx) => (
									<button
										key={idx}
										className={cn(
											styles.items__thumb,
											idx === currentMediaIdx ? styles.active : ''
										)}
										onClick={() => setCurrentMediaIdx(idx)}
									>
										<Image media={media as unknown as IMediaProps} alt="" />
									</button>
								))}
							</div>
							<button className={styles.items__nav} onClick={handleNextMedia}>
								<Icon type="cms" variant="chevron-right" />
							</button>
						</div>
					)}
				</div>

				{!isBuilderMode && (
					<div className={styles.toolbar}>
						<div className={styles.toolbar__list}>
							{filteredMediaToolbar.map((tool, idx) => (
								<button
									key={idx}
									className={styles.toolbar__button}
									onClick={() => handleAction(tool.action)}
									title={tool.name}
								>
									<Icon type="cms" variant={tool.icon} />
								</button>
							))}
						</div>

						<div className={styles.toolbar__fixed}>
							{!isEdit ? (
								<button
									className={cn(styles.toolbar__button, styles.text)}
									title="Edit"
									onClick={handleEdit}
								>
									Edit
								</button>
							) : (
								<button
									className={cn(styles.toolbar__button, styles.text)}
									title="Back"
									onClick={handleBack}
								>
									<Icon type="cms" variant="back" />
								</button>
							)}
						</div>
					</div>
				)}
			</div>
			{isEdit &&
				activeMediaId === mediaId &&
				((multiple && checkArr(propsValue) && propsValue.length > 0) ||
					(!multiple && propsValue)) && (
					<MediaInfoLayer
						multiple={multiple}
						toolbar={filteredMediaToolbar}
						mediaList={propsValue}
						propsValue={propsValue}
						setPropsValue={setPropsValue}
						currentMediaIdx={currentMediaIdx}
						setCurrentMediaIdx={setCurrentMediaIdx}
						onChange={onChange}
						field={props.field}
					/>
				)}
		</div>
	)
}
