/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")),\n                \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNDREElNUMlNUNyZXBvcyU1QyU1Q2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUlNUMlNUNhcHBzJTVDJTVDY29sbGVjdC1jbXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNub3QtZm91bmQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBZ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8/OTZhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXENEQVxcXFxyZXBvc1xcXFxicmFuZC1jb21wYXNzLWZyb250ZW5kLXRlbXBsYXRlXFxcXGFwcHNcXFxcY29sbGVjdC1jbXNcXFxcc3JjXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.scss%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CGeneralSettingContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CQuickNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Cstyles%5C%5Cglobal.scss%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.scss%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CGeneralSettingContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CQuickNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Cstyles%5C%5Cglobal.scss%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx */ \"(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/ui-lib/src/contexts/NavigationContext.tsx */ \"(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx */ \"(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.scss%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CGeneralSettingContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CQuickNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Cstyles%5C%5Cglobal.scss%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"(ssr)/./node_modules/next/error.js\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Render the default Next.js 404 page when a route\n// is requested that doesn't match the middleware and\n// therefore doesn't have a locale associated with it.\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n                statusCode: 404\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRThCO0FBRTlCLG1EQUFtRDtBQUNuRCxxREFBcUQ7QUFDckQsc0RBQXNEO0FBRXZDLFNBQVNDO0lBQ3ZCLHFCQUNDLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNWLDRFQUFDQztzQkFDQSw0RUFBQ0osbURBQUtBO2dCQUFDSyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi9zcmMvYXBwL25vdC1mb3VuZC50c3g/Y2FlMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IEVycm9yIGZyb20gJ25leHQvZXJyb3InXG5cbi8vIFJlbmRlciB0aGUgZGVmYXVsdCBOZXh0LmpzIDQwNCBwYWdlIHdoZW4gYSByb3V0ZVxuLy8gaXMgcmVxdWVzdGVkIHRoYXQgZG9lc24ndCBtYXRjaCB0aGUgbWlkZGxld2FyZSBhbmRcbi8vIHRoZXJlZm9yZSBkb2Vzbid0IGhhdmUgYSBsb2NhbGUgYXNzb2NpYXRlZCB3aXRoIGl0LlxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcblx0cmV0dXJuIChcblx0XHQ8aHRtbCBsYW5nPVwiZW5cIj5cblx0XHRcdDxib2R5PlxuXHRcdFx0XHQ8RXJyb3Igc3RhdHVzQ29kZT17NDA0fSAvPlxuXHRcdFx0PC9ib2R5PlxuXHRcdDwvaHRtbD5cblx0KVxufVxuIl0sIm5hbWVzIjpbIkVycm9yIiwiTm90Rm91bmQiLCJodG1sIiwibGFuZyIsImJvZHkiLCJzdGF0dXNDb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx":
/*!********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeneralSettingContext: () => (/* binding */ GeneralSettingContext),\n/* harmony export */   \"default\": () => (/* binding */ GeneralSettingProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GeneralSettingContext,default auto */ \n\nconst defaultContext = {\n    locale: \"en\",\n    Social: []\n};\nconst GeneralSettingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction GeneralSettingProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GeneralSettingContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\contexts\\\\GeneralSettingContext.tsx\",\n        lineNumber: 20,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9jb250ZXh0cy9HZW5lcmFsU2V0dGluZ0NvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFHcUM7QUFFckMsTUFBTUMsaUJBQXNDO0lBQzNDQyxRQUFRO0lBQ1JDLFFBQVEsRUFBRTtBQUNYO0FBRU8sTUFBTUMsc0NBQXdCSixvREFBYUEsQ0FBQ0MsZ0JBQWU7QUFFbkQsU0FBU0ksdUJBQXVCLEVBQzlDQyxJQUFJLEVBQ0pDLFFBQVEsRUFJUjtJQUNBLHFCQUFPLDhEQUFDSCxzQkFBc0JJLFFBQVE7UUFBQ0MsT0FBT0g7a0JBQU9DOzs7Ozs7QUFDdEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2NvbnRleHRzL0dlbmVyYWxTZXR0aW5nQ29udGV4dC50c3g/MzJmOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHR5cGUgeyBHZW5lcmFsU2V0dGluZ1Byb3BzIH0gZnJvbSAnQGNvbGxlY3RpdmUvaW50ZWdyYXRpb24tbGliL3NlYXJjaCdcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgZGVmYXVsdENvbnRleHQ6IEdlbmVyYWxTZXR0aW5nUHJvcHMgPSB7XG5cdGxvY2FsZTogJ2VuJyxcblx0U29jaWFsOiBbXSxcbn1cblxuZXhwb3J0IGNvbnN0IEdlbmVyYWxTZXR0aW5nQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdlbmVyYWxTZXR0aW5nUHJvdmlkZXIoe1xuXHRkYXRhLFxuXHRjaGlsZHJlbixcbn06IHtcblx0ZGF0YTogR2VuZXJhbFNldHRpbmdQcm9wc1xuXHRjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG5cdHJldHVybiA8R2VuZXJhbFNldHRpbmdDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtkYXRhfT57Y2hpbGRyZW59PC9HZW5lcmFsU2V0dGluZ0NvbnRleHQuUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsImRlZmF1bHRDb250ZXh0IiwibG9jYWxlIiwiU29jaWFsIiwiR2VuZXJhbFNldHRpbmdDb250ZXh0IiwiR2VuZXJhbFNldHRpbmdQcm92aWRlciIsImRhdGEiLCJjaGlsZHJlbiIsIlByb3ZpZGVyIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx":
/*!****************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/NavigationContext.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   \"default\": () => (/* binding */ NavigationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NavigationContext,default auto */ \n\nconst GLOBAL_ROUTE = [\n    {\n        label: \"Our Podcast\",\n        path: \"/our-podcast\",\n        isLocked: false\n    },\n    {\n        label: \"Work with us\",\n        path: \"/work-with-us\",\n        isLocked: false\n    },\n    {\n        label: \"Newsletters\",\n        path: \"/newsletters\",\n        isLocked: false\n    }\n];\nconst defaultContext = GLOBAL_ROUTE;\nconst NavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction NavigationProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\contexts\\\\NavigationContext.tsx\",\n        lineNumber: 35,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickNavigationContext: () => (/* binding */ QuickNavigationContext),\n/* harmony export */   \"default\": () => (/* binding */ NavigationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuickNavigationContext,default auto */ \n\nconst GLOBAL_ROUTE = [\n    {\n        label: \"Terms and Services\",\n        path: \"/terms-of-services\",\n        isLocked: false\n    },\n    {\n        label: \"Privacy\",\n        path: \"/privacy\",\n        isLocked: false\n    }\n];\nconst defaultContext = GLOBAL_ROUTE;\nconst QuickNavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction NavigationProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuickNavigationContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\contexts\\\\QuickNavigationContext.tsx\",\n        lineNumber: 30,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9jb250ZXh0cy9RdWlja05hdmlnYXRpb25Db250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBR3FDO0FBRXJDLE1BQU1DLGVBQWU7SUFDcEI7UUFDQ0MsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFVBQVU7SUFDWDtJQUNBO1FBQ0NGLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxVQUFVO0lBQ1g7Q0FDQTtBQUVELE1BQU1DLGlCQUFxQ0o7QUFFcEMsTUFBTUssdUNBQXlCTixvREFBYUEsQ0FBQ0ssZ0JBQWU7QUFFcEQsU0FBU0UsbUJBQW1CLEVBQzFDQyxJQUFJLEVBQ0pDLFFBQVEsRUFJUjtJQUNBLHFCQUFPLDhEQUFDSCx1QkFBdUJJLFFBQVE7UUFBQ0MsT0FBT0g7a0JBQU9DOzs7Ozs7QUFDdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2NvbnRleHRzL1F1aWNrTmF2aWdhdGlvbkNvbnRleHQudHN4P2Y1N2YiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB0eXBlIHsgSU5hdmlnYXRpb25Qcm9wcyB9IGZyb20gJ0Bjb2xsZWN0aXZlL2ludGVncmF0aW9uLWxpYi9jbXMnXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnXG5cbmNvbnN0IEdMT0JBTF9ST1VURSA9IFtcblx0e1xuXHRcdGxhYmVsOiAnVGVybXMgYW5kIFNlcnZpY2VzJyxcblx0XHRwYXRoOiAnL3Rlcm1zLW9mLXNlcnZpY2VzJyxcblx0XHRpc0xvY2tlZDogZmFsc2UsXG5cdH0sXG5cdHtcblx0XHRsYWJlbDogJ1ByaXZhY3knLFxuXHRcdHBhdGg6ICcvcHJpdmFjeScsXG5cdFx0aXNMb2NrZWQ6IGZhbHNlLFxuXHR9LFxuXVxuXG5jb25zdCBkZWZhdWx0Q29udGV4dDogSU5hdmlnYXRpb25Qcm9wc1tdID0gR0xPQkFMX1JPVVRFXG5cbmV4cG9ydCBjb25zdCBRdWlja05hdmlnYXRpb25Db250ZXh0ID0gY3JlYXRlQ29udGV4dChkZWZhdWx0Q29udGV4dClcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmF2aWdhdGlvblByb3ZpZGVyKHtcblx0ZGF0YSxcblx0Y2hpbGRyZW4sXG59OiB7XG5cdGRhdGE6IElOYXZpZ2F0aW9uUHJvcHNbXVxuXHRjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG5cdHJldHVybiA8UXVpY2tOYXZpZ2F0aW9uQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17ZGF0YX0+e2NoaWxkcmVufTwvUXVpY2tOYXZpZ2F0aW9uQ29udGV4dC5Qcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwiR0xPQkFMX1JPVVRFIiwibGFiZWwiLCJwYXRoIiwiaXNMb2NrZWQiLCJkZWZhdWx0Q29udGV4dCIsIlF1aWNrTmF2aWdhdGlvbkNvbnRleHQiLCJOYXZpZ2F0aW9uUHJvdmlkZXIiLCJkYXRhIiwiY2hpbGRyZW4iLCJQcm92aWRlciIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.scss":
/*!*********************************!*\
  !*** ./src/styles/globals.scss ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c6e438c2de43\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuc2NzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4vc3JjL3N0eWxlcy9nbG9iYWxzLnNjc3M/YzRmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImM2ZTQzOGMyZGU0M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.scss\n");

/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/styles/global.scss":
/*!****************************************************!*\
  !*** ../../packages/ui-lib/src/styles/global.scss ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"79eccf42ccf4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9zdHlsZXMvZ2xvYmFsLnNjc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL3N0eWxlcy9nbG9iYWwuc2Nzcz82NzJlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzllY2NmNDJjY2Y0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../packages/ui-lib/src/styles/global.scss\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.scss */ \"(rsc)/./src/styles/globals.scss\");\n/* harmony import */ var _collective_ui_lib_styles_global_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @collective/ui-lib/styles/global.scss */ \"(rsc)/../../packages/ui-lib/src/styles/global.scss\");\n/* harmony import */ var _collective_ui_lib_contexts_GeneralSettingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @collective/ui-lib/contexts/GeneralSettingContext */ \"(rsc)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx\");\n/* harmony import */ var _collective_ui_lib_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @collective/ui-lib/contexts/NavigationContext */ \"(rsc)/../../packages/ui-lib/src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _collective_ui_lib_contexts_QuickNavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @collective/ui-lib/contexts/QuickNavigationContext */ \"(rsc)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx\");\n\n\n\n\n\n\nasync function RootLayout({ children }) {\n    // const [generalSetting, nav, quickNav] = await Promise.all([\n    // \tsearchGeneralSetting(),\n    // \tgetNavigationData(),\n    // \tgetNavigationData('2'),\n    // ])\n    const [generalSetting, nav, quickNav] = [\n        {\n            hits: [\n                {}\n            ]\n        },\n        [],\n        []\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_collective_ui_lib_contexts_GeneralSettingContext__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        data: generalSetting.hits[0],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_collective_ui_lib_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            data: nav,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_collective_ui_lib_contexts_QuickNavigationContext__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                data: quickNav,\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\apps\collect-cms\src\app\not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx":
/*!********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GeneralSettingContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\GeneralSettingContext.tsx#GeneralSettingContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\GeneralSettingContext.tsx#default`));


/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/contexts/NavigationContext.tsx":
/*!****************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/NavigationContext.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NavigationContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\NavigationContext.tsx#NavigationContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\NavigationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuickNavigationContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\QuickNavigationContext.tsx#QuickNavigationContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\QuickNavigationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uL3NyYy9hcHAvZmF2aWNvbi5pY28/ZGI0NSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();